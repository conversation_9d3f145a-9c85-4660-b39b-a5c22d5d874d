  // Processing State Management Types

// Individual processing stage status
export type ProcessingStatus = 
  | 'pending'
  | 'running'
  | 'completed'
  | 'failed'
  | 'error';

export type ProcessingStage =
  | 'email_validation'
  | 'contact_enrichment_v2'
  | 'contact_investment_criteria'
  | 'email_generation'
  | 'smartlead_sync'
  | 'job_tiering'
  | 'company_overview_v2'
  | 'company_investment_criteria'
  | 'website_scraping'
  | 'article_html_fetch'
  | 'article_link_fetch'
  | 'article_enrichment';

// Base stats for each processing stage
export interface StageStats {
  total: number;
  pending: number;
  running: number;
  completed: number;
  failed: number;
  error: number;
  success_rate: number;
  last_processed?: string;
}



export interface ProcessingJob {
  id: string;
  type: ProcessingStage;
  entity_type: 'contact' | 'company' | 'article';
  entity_id: number;
  status: ProcessingStatus;
  priority: number;
  created_at: string;
  started_at?: string;
  completed_at?: string;
  error_message?: string;
  metadata?: Record<string, any>;
}

export interface EntityData {
  id: number
}

export interface ProcessingFilters {
  // Basic filters
  source?: string | string[]
  dateRange?: string
  
  // Contact processing status filters
  contact_enrichment_v2_status?: string
  contact_email_verification_status?: string
  email_generation_status?: string
  email_sending_status?: string
  smartlead_sync_status?: string

  // Company processing status filters
  company_overview_v2_status?: string
  website_scraping_status?: string
  
  // Contact-specific filters
  emailStatus?: string[]
  contactCompanyType?: string[]
  contactCapitalPosition?: string[]
  jobTier?: string[]
  
  // Geographic filters
  contactCountries?: string[]
  contactStates?: string[]
  contactCities?: string[]
  regions?: string[]
  states?: string[]
  cities?: string[]
  countries?: string[]
  
  // Investment criteria filters
  capitalPosition?: string[]
  propertyTypes?: string[]
  strategies?: string[]
  dealSizeMin?: number
  dealSizeMax?: number
  targetReturnMin?: number
  targetReturnMax?: number
  
  // Boolean flags
  extracted?: boolean
  searched?: boolean
  emailGenerated?: boolean
  enriched?: boolean
  hasSmartleadId?: boolean
  hasBeenReachedOut?: boolean
}

export interface ProcessorOptions {
  limit?: number
  singleId?: number
  multiIds?: number[]
  filters?: ProcessingFilters
  batchSize?: number
  batchDelay?: number // Delay between batches in milliseconds (deprecated - use bottleneckConfig instead)
  entityType?: 'contact' | 'company' | 'both'
  campaignId?: string // Add campaign ID for email generation and Smartlead sync
  bottleneckConfig?: import('../lib/processors/BaseProcessor').BottleneckConfig // Processor-specific Bottleneck configuration
}

export interface ProcessorResult {
  processed: number
  successful: number
  failed: number
  errors: string[]
}

export interface MappingData {
  [key: string]: string[]
}


// Unified entity data that combines contact and company information
export interface UnifiedEntityData extends EntityData {
  // Common fields
  id: number // Primary ID (contact_id or company_id)
  entity_type: 'contact' | 'company'
  
  // Contact-specific fields (when entity_type is 'contact')
  contact_id?: number
  first_name?: string
  last_name?: string
  full_name?: string
  title?: string
  email?: string
  linkedin_url?: string
  company_id?: number
  contact_country?: string
  contact_city?: string
  contact_state?: string
  phone_number?: string
  
  // Company-specific fields (when entity_type is 'company')
  company_name?: string
  company_website?: string
  industry?: string
  
  // Shared fields (available for both types)
  source?: string
  created_at?: string
  updated_at?: string
}
