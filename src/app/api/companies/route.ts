import { pool } from '@/lib/db'  
import { NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

// Helper function to safely parse numeric fields from strings
const safeParseNumeric = (value: string | null | undefined): number | null => {
  if (value === null || value === undefined) return null
  // Remove non-numeric characters except for the decimal point
  const cleaned = String(value).replace(/[^0-9.]/g, '')
  // Handle cases with multiple decimal points by taking the first part
  const parts = cleaned.split('.')
  const a = parts.shift()
  const b = parts.join('')
  const final = parseFloat(`${a}.${b}`)
  return isNaN(final) ? null : final
}

export async function GET(request: Request) {
  try {
    // For development, we'll skip the authentication check
    // const session = await getServerSession(authOptions)
    // if (!session) {
    //   return new NextResponse(JSON.stringify({ error: 'Unauthorized' }), {
    //     status: 401,
    //     headers: { 'Content-Type': 'application/json' }
    //   })
    // }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1', 10)
    const limit = parseInt(searchParams.get('limit') || '20', 10)
    const offset = (page - 1) * limit
    const search = searchParams.get('search') || ''
    const sortField = searchParams.get('sort') || 'updated_at'
    const sortDirection = searchParams.get('direction') || 'desc'

    // Parse filter parameters
    const capitalTypes = searchParams.get('capitalTypes') ? JSON.parse(searchParams.get('capitalTypes') || '[]') : []
    const loanTypes = searchParams.get('loanTypes') ? JSON.parse(searchParams.get('loanTypes') || '[]') : []
    const propertyTypes = searchParams.get('propertyTypes') ? JSON.parse(searchParams.get('propertyTypes') || '[]') : []
    const assetTypes = searchParams.get('assetTypes') ? JSON.parse(searchParams.get('assetTypes') || '[]') : []
    const regions = searchParams.get('regions') ? JSON.parse(searchParams.get('regions') || '[]') : []
    const states = searchParams.get('states') ? JSON.parse(searchParams.get('states') || '[]') : []
    const cities = searchParams.get('cities') ? JSON.parse(searchParams.get('cities') || '[]') : []
    const countries = searchParams.get('countries') ? JSON.parse(searchParams.get('countries') || '[]') : []
    
    // Parse range filters
    const dealSizeMin = parseFloat(searchParams.get('dealSize_min') || '0')
    const dealSizeMax = parseFloat(searchParams.get('dealSize_max') || '1000')
    const ltvMin = parseFloat(searchParams.get('ltv_min') || '0')
    const ltvMax = parseFloat(searchParams.get('ltv_max') || '100')
    const ltcMin = parseFloat(searchParams.get('ltc_min') || '0')
    const ltcMax = parseFloat(searchParams.get('ltc_max') || '100')

    // Build the WHERE clause
    const conditions: string[] = []
    const params: any[] = []
    let paramIndex = 1

    // Search condition
    if (search) {
      conditions.push(`c.company_name ILIKE $${paramIndex}`)
      params.push(`%${search}%`)
      paramIndex++
    }

    // V2 filters now use investment_criteria_central table instead of company_extracted_data
    // These filters should be handled by the unified-filters-v2 endpoint
    // For now, we'll remove these complex filters from the basic companies endpoint
    
    // Geographic location filter (basic)
    if (regions.length > 0 || states.length > 0 || cities.length > 0 || countries.length > 0) {
      const allLocations = [...regions, ...states, ...cities, ...countries]
      const locationParams = allLocations.map((_: string, i: number) => `$${paramIndex + i}`).join(', ')
      conditions.push(`(
        c.company_city ILIKE ANY(ARRAY[${locationParams}]) OR
        c.company_state ILIKE ANY(ARRAY[${locationParams}]) OR
        c.company_country ILIKE ANY(ARRAY[${locationParams}])
      )`)
      params.push(...allLocations.map(loc => `%${loc}%`))
      paramIndex += allLocations.length
    }

    // // LTV range filter
    // if (ltvMin > 0 || ltvMax < 100) {
    //   conditions.push(`
    //     safe_parse_numeric(ced.max_ltv) BETWEEN $${paramIndex} AND $${paramIndex + 1}
    //   `)
    //   params.push(ltvMin, ltvMax)
    //   paramIndex += 2
    // }

    // // LTC range filter
    // if (ltcMin > 0 || ltcMax < 100) {
    //   conditions.push(`
    //     safe_parse_numeric(ced.max_ltc) BETWEEN $${paramIndex} AND $${paramIndex + 1}
    //   `)
    //   params.push(ltcMin, ltcMax)
    //   paramIndex += 2
    // }

    // Combine all conditions with AND
    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : ''

    // Validate sort field to prevent SQL injection
    const allowedSortFields = [
      'company_name', 'company_website', 'industry', 'created_at', 'updated_at', 
      'companytype', 'minimumdealsize', 'maximumdealsize', 'dealsize', 'aum',
      'founded_year', 'foundedyear', 'contact_count', 'holdperiod', 'targetreturn',
      'numberofproperties', 'numberofoffices', 'numberofemployees', 'businessmodel',
      'fundsize', 'headquarters'
    ]
    
    // Map client-side sort field names to database column names
    const sortFieldMap: Record<string, string> = {
      'name': 'company_name',
      'website': 'company_website',
      'description': 'summary',
      'dealsizemin': 'minimumdealsize',
      'dealsizemax': 'maximumdealsize'
    }
    
    // Get the actual database column name for sorting
    const dbSortField = sortFieldMap[sortField] || sortField
    
    const sanitizedSortField = allowedSortFields.includes(dbSortField) 
      ? dbSortField 
      : 'updated_at'
    
    // Validate sort direction
    const sanitizedSortDirection = ['asc', 'desc'].includes(sortDirection.toLowerCase()) 
      ? sortDirection.toLowerCase() 
      : 'desc'

    // All sorting now uses companies table only in V2
    const sortTable = 'c'

    // Create the helper function in the database
    await pool.query(`
      CREATE OR REPLACE FUNCTION safe_parse_numeric(value TEXT)
      RETURNS NUMERIC AS $$
      BEGIN
        RETURN CAST(regexp_replace(value, '[^0-9.]', '', 'g') AS NUMERIC);
      EXCEPTION WHEN OTHERS THEN
        RETURN NULL;
      END;
      $$ LANGUAGE plpgsql;
    `)

    // Build the query - V2 using only companies table
    const query = `
      SELECT 
        c.company_id, 
        c.company_name, 
        c.company_website, 
        c.industry,
        c.founded_year,
        c.company_address,
        c.company_city,
        c.company_state,
        c.company_zip,
        c.company_country,
        c.company_linkedin as linkedin_url,
        c.created_at,
        c.updated_at,
        (SELECT COUNT(*) FROM contacts WHERE company_id = c.company_id) as contact_count,
        -- V2 Company Overview fields from companies table
        c.company_type as companytype,
        c.business_model as businessmodel,
        c.fund_size as fundsize,
        c.aum,
        c.number_of_properties as numberofproperties,
        c.company_address as headquarters,
        c.number_of_offices as numberofoffices,
        c.founded_year as foundedyear,
        c.number_of_employees as numberofemployees,
        c.investment_focus as investmentfocus,
        c.partnerships,
        c.company_phone as mainphone,
        c.main_email as mainemail,
        c.investment_strategy_mission as mission,
        c.investment_strategy_approach as approach,
        c.annual_revenue,
        c.market_capitalization,
        c.data_confidence_score,
        c.website_scraping_status,
        c.company_overview_status,
        c.overview_v2_status,
        c.investment_criteria_status,
        -- Additional V2 fields
        c.key_executives,
        c.major_competitors,
        c.company_history,
        c.products_services_description,
        c.target_customer_profile,
        c.office_locations,
        c.transactions_completed_last_12m,
        c.portfolio_size_sqft,
        c.portfolio_asset_count,
        c.dry_powder,
        c.annual_deployment_target
      FROM 
        companies c
      ${whereClause}
      ORDER BY 
        c.${sanitizedSortField} ${sanitizedSortDirection}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `

    const countQuery = `
      SELECT COUNT(*) as total
      FROM companies c
      ${whereClause}
    `

    // Execute the queries
    const result = await pool.query(query, [...params, limit, offset])
    const countResult = await pool.query(countQuery, params)
    
    const total = parseInt(countResult.rows[0].total, 10)
    const totalPages = Math.ceil(total / limit)

    return NextResponse.json({
      companies: result.rows,
      pagination: {
        page,
        limit,
        total,
        totalPages
      }
    })
  } catch (error) {
    console.error('Error fetching companies:', error)
    return new NextResponse(JSON.stringify({ error: 'Failed to fetch companies' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    })
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json()
    
    // Validate required fields
    if (!body.company_name || body.company_name.trim() === '') {
      return NextResponse.json(
        { error: 'Company name is required' },
        { status: 400 }
      )
    }
    
    // Start a transaction
    const client = await pool.connect()
    
    try {
      await client.query('BEGIN')
      
      // Check for existing company with same name to avoid unique constraint violation
      const checkExistingQuery = `
        SELECT company_id FROM companies 
        WHERE lower(company_name) = lower($1)
      `
      const existingResult = await client.query(checkExistingQuery, [
        body.company_name.trim()
      ])
      
      if (existingResult.rows.length > 0) {
        return NextResponse.json(
          { error: 'A company with this name already exists' },
          { status: 409 }
        )
      }
      
      // Insert basic company information
      const companyQuery = `
        INSERT INTO companies (
          company_name,
          company_type,
          industry,
          business_model,
          investment_focus,
          investment_strategy_mission,
          investment_strategy_approach,
          company_website,
          company_phone,
          secondary_phone,
          main_email,
          secondary_email,
          company_linkedin,
          twitter,
          facebook,
          instagram,
          youtube,
          company_address,
          company_city,
          company_state,
          company_zip,
          company_country,
          additional_address,
          additional_city,
          additional_state,
          additional_zipcode,
          additional_country,
          fund_size,
          aum,
          number_of_properties,
          number_of_offices,
          office_locations,
          founded_year,
          number_of_employees,
          partnerships,
          balance_sheet_strength,
          funding_sources,
          recent_capital_raises,
          typical_debt_to_equity_ratio,
          development_fee_structure,
          key_equity_partners,
          key_debt_partners,
          market_cycle_positioning,
          urban_vs_suburban_preference,
          sustainability_esg_focus,
          technology_proptech_adoption,
          adaptive_reuse_experience,
          regulatory_zoning_expertise,
          investment_vehicle_type,
          active_fund_name_series,
          fund_size_active_fund,
          fundraising_status,
          lender_type,
          annual_loan_volume,
          lending_origin,
          portfolio_health,
          board_of_directors,
          key_executives,
          founder_background,
          company_history,
          stock_ticker_symbol,
          stock_exchange,
          market_capitalization,
          annual_revenue,
          net_income,
          ebitda,
          profit_margin,
          credit_rating,
          quarterly_earnings_link,
          products_services_description,
          target_customer_profile,
          major_competitors,
          market_share_percentage,
          unique_selling_proposition,
          industry_awards_recognitions,
          corporate_structure,
          parent_company,
          subsidiaries,
          dry_powder,
          annual_deployment_target,
          transactions_completed_last_12m,
          internal_relationship_manager,
          last_contact_date,
          pipeline_status,
          role_in_previous_deal,
          total_transaction_volume_ytd,
          deal_count_ytd,
          average_deal_size,
          portfolio_size_sqft,
          portfolio_asset_count,
          recent_news_sentiment,
          data_source,
          last_updated_timestamp,
          data_confidence_score,
          source
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22, $23, $24, $25, $26, $27, $28, $29, $30, $31, $32, $33, $34, $35, $36, $37, $38, $39, $40, $41, $42, $43, $44, $45, $46, $47, $48, $49, $50, $51, $52, $53, $54, $55, $56, $57, $58, $59, $60, $61, $62, $63, $64, $65, $66, $67, $68, $69, $70, $71, $72, $73, $74, $75, $76, $77, $78, $79, $80, $81, $82, $83, $84, $85, $86, $87, $88, $89, $90, $91, $92, $93, $94, $95)
        RETURNING company_id
      `
      
      // Helper function to format arrays for PostgreSQL
      const formatArrayForDB = (arr) => {
        if (!arr || !Array.isArray(arr) || arr.length === 0) {
          return null;
        }
        return arr;
      };

      const companyValues = [
        body.company_name,
        body.company_type || null,
        body.industry || null,
        body.business_model || null,
        formatArrayForDB(body.investment_focus),
        body.investment_strategy_mission || null,
        body.investment_strategy_approach || null,
        body.company_website || body.website || null,
        body.company_phone || body.main_phone || null,
        body.secondary_phone || null,
        body.main_email || null,
        body.secondary_email || null,
        body.company_linkedin || body.linkedin || null,
        body.twitter || null,
        body.facebook || null,
        body.instagram || null,
        body.youtube || null,
        body.company_address || body.headquarters_address || null,
        body.company_city || body.headquarters_city || null,
        body.company_state || body.headquarters_state || null,
        body.company_zip || body.headquarters_zipcode || null,
        body.company_country || body.headquarters_country || null,
        body.additional_address || null,
        body.additional_city || null,
        body.additional_state || null,
        body.additional_zipcode || null,
        body.additional_country || null,
        body.fund_size || null,
        body.aum || null,
        body.number_of_properties || null,
        body.number_of_offices || null,
        formatArrayForDB(body.office_locations),
        body.founded_year || null,
        body.number_of_employees || null,
        formatArrayForDB(body.partnerships),
        body.balance_sheet_strength || null,
        formatArrayForDB(body.funding_sources),
        body.recent_capital_raises || null,
        body.typical_debt_to_equity_ratio || null,
        body.development_fee_structure || null,
        formatArrayForDB(body.key_equity_partners),
        formatArrayForDB(body.key_debt_partners),
        body.market_cycle_positioning || null,
        body.urban_vs_suburban_preference || null,
        body.sustainability_esg_focus || false,
        body.technology_proptech_adoption || false,
        body.adaptive_reuse_experience || false,
        body.regulatory_zoning_expertise || false,
        body.investment_vehicle_type || null,
        body.active_fund_name_series || null,
        body.fund_size_active_fund || null,
        body.fundraising_status || null,
        body.lender_type || null,
        body.annual_loan_volume || null,
        body.lending_origin_balance_sheet_securitization || body.lending_origin || null,
        body.portfolio_health || null,
        formatArrayForDB(body.board_of_directors),
        formatArrayForDB(body.key_executives),
        body.founder_background || null,
        body.company_history || null,
        body.stock_ticker_symbol || null,
        body.stock_exchange || null,
        body.market_capitalization || null,
        body.annual_revenue || null,
        body.net_income || null,
        body.ebitda || null,
        body.profit_margin || null,
        body.credit_rating || null,
        body.quarterly_earnings_link || null,
        body.products_services_description || null,
        body.target_customer_profile || null,
        formatArrayForDB(body.major_competitors),
        body.market_share_percentage || null,
        body.unique_selling_proposition || null,
        formatArrayForDB(body.industry_awards_recognitions),
        body.corporate_structure || null,
        body.parent_company || null,
        formatArrayForDB(body.subsidiaries),
        body.dry_powder || null,
        body.annual_deployment_target || null,
        body.transactions_completed_last_12m || null,
        body.internal_relationship_manager || null,
        body.last_contact_date || null,
        body.pipeline_status || null,
        body.role_in_previous_deal || null,
        body.total_transaction_volume_ytd || null,
        body.deal_count_ytd || null,
        body.average_deal_size || null,
        body.portfolio_size_sqft || null,
        body.portfolio_asset_count || null,
        body.recent_news_sentiment || null,
        body.data_source || null,
        body.last_updated_timestamp || null,
        body.data_confidence_score || null,
        body.source || 'manual'  // Default source to 'manual' if not provided
      ]

      const companyResult = await client.query(companyQuery, companyValues)
      const company_id = companyResult.rows[0].company_id
      
      // No need for separate extracted data table - all data is now in the main companies table
      
      // Commit the transaction
      await client.query('COMMIT')
      
      // Return the new company ID
      return NextResponse.json({
        company_id: company_id
      })
    } catch (error) {
      // Rollback in case of error
      await client.query('ROLLBACK')
      throw error
    } finally {
      // Release the client back to the pool
      client.release()
    }
  } catch (error) {
    console.error('Database error:', error)
    return NextResponse.json(
      { error: 'Failed to create company' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: Request) {
  try {
    // For development, we'll skip the authentication check
    // const session = await getServerSession(authOptions)
    // if (!session) {
    //   return new NextResponse(JSON.stringify({ error: 'Unauthorized' }), {
    //     status: 401,
    //     headers: { 'Content-Type': 'application/json' }
    //   })
    // }

    const { searchParams } = new URL(request.url)
    const companyId = searchParams.get('companyId')

    if (!companyId) {
      return NextResponse.json(
        { error: 'Company ID is required' },
        { status: 400 }
      )
    }

    // Start a transaction to ensure data consistency
    const client = await pool.connect()
    
    try {
      await client.query('BEGIN')
      
      // Check if company exists
      const checkQuery = 'SELECT company_id, company_name FROM companies WHERE company_id = $1'
      const checkResult = await client.query(checkQuery, [companyId])
      
      if (checkResult.rows.length === 0) {
        return NextResponse.json(
          { error: 'Company not found' },
          { status: 404 }
        )
      }

      const companyName = checkResult.rows[0].company_name

      // Delete related records first (due to foreign key constraints)
      // Delete investment criteria
      //delete all equity and debt investment criteria records associated with this company central table
      await client.query(
        'DELETE FROM investment_criteria_central WHERE entity_type = $1 AND entity_id = $2',
        ['company', companyId]
      )


      // Delete company extracted data
      await client.query(
        'DELETE FROM company_extracted_data WHERE company_id = $1',
        [companyId]
      )

      // Delete contacts associated with this company (optional - you might want to keep contacts and just set company_id to null)
      // await client.query(
      //   'DELETE FROM contacts WHERE company_id = $1',
      //   [companyId]
      // )

      // Finally, delete the company
      await client.query(
        'DELETE FROM companies WHERE company_id = $1',
        [companyId]
      )

      await client.query('COMMIT')
      
      return NextResponse.json({
        success: true,
        message: `Company "${companyName}" has been successfully deleted`,
        deletedCompanyId: companyId
      })
    } catch (error) {
      await client.query('ROLLBACK')
      throw error
    } finally {
      client.release()
    }
  } catch (error) {
    console.error('Error deleting company:', error)
    return NextResponse.json(
      { error: 'Failed to delete company' },
      { status: 500 }
    )
  }
} 