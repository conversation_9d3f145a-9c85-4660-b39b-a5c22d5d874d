import { NextRequest, NextResponse } from 'next/server'
import { pool } from '@/lib/db'

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: companyId } = await params
    
    if (!companyId) {
      return NextResponse.json(
        { error: 'Company ID is required' },
        { status: 400 }
      )
    }

    const client = await pool.connect()
    
    try {
      // First, count existing web pages
      const countQuery = `
        SELECT COUNT(*) as page_count
        FROM company_web_pages
        WHERE company_id = $1
      `
      const countResult = await client.query(countQuery, [companyId])
      const pageCount = parseInt(String(countResult.rows[0]?.page_count) || '0', 10)
      
      if (pageCount === 0) {
        return NextResponse.json({
          success: true,
          message: 'No web pages found for this company',
          deletedPages: 0
        })
      }

      // Delete all web pages for the company
      const deleteQuery = `
        DELETE FROM company_web_pages
        WHERE company_id = $1
      `
      
      const deleteResult = await client.query(deleteQuery, [companyId])
      
      return NextResponse.json({
        success: true,
        message: `Successfully deleted ${pageCount} web pages for company ${companyId}`,
        deletedPages: pageCount
      })
      
    } finally {
      client.release()
    }
    
  } catch (error) {
    console.error('Error deleting web pages:', error)
    return NextResponse.json(
      { 
        error: 'Failed to delete web pages',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
