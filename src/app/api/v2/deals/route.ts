import { NextRequest, NextResponse } from "next/server";
import { typeORMService } from "@/lib/typeorm/service";
import { withTypeOR<PERSON><PERSON>and<PERSON> } from "@/lib/typeorm/middleware";
import { ILike, FindOptionsWhere, FindManyOptions, In } from "typeorm";

async function getDealsHandler(request: NextRequest): Promise<NextResponse> {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const pageSize = parseInt(searchParams.get("pageSize") || "20");
    const search = searchParams.get("search") || "";
    const sortBy = searchParams.get("sortBy") || "updated_at";
    const sortOrder = searchParams.get("sortOrder") || "DESC";

    // Get repositories from the service
    const dealsRepository = typeORMService.getDealsRepository();
    const nsfRepository = typeORMService.getNsfRepository();

    // Build search conditions
    const whereConditions: FindOptionsWhere<any> = {};
    if (search) {
      whereConditions.dealName = ILike(`%${search}%`);
    }

    // Add support for additional filters with normalization and URL decoding
    const dealStageParam = searchParams.get("dealStage");
    const dealStatusParam = searchParams.get("dealStatus");
    const dealName = searchParams.get("dealName") ? decodeURIComponent(searchParams.get("dealName")!).trim() : undefined;
    const sponsorName = searchParams.get("sponsorName") ? decodeURIComponent(searchParams.get("sponsorName")!).trim() : undefined;
    const address = searchParams.get("address") ? decodeURIComponent(searchParams.get("address")!).trim() : undefined;
    const contactName = searchParams.get("contactName") ? decodeURIComponent(searchParams.get("contactName")!).trim() : undefined;
    const contactCompany = searchParams.get("contactCompany") ? decodeURIComponent(searchParams.get("contactCompany")!).trim() : undefined;
    const neighborhood = searchParams.get("neighborhood") ? decodeURIComponent(searchParams.get("neighborhood")!).trim() : undefined;
    const zipCode = searchParams.get("zipCode") ? decodeURIComponent(searchParams.get("zipCode")!).trim() : undefined;
    const capitalPosition = searchParams.get("capitalPosition")?.split(',').filter(v => v);
    const sourceType = searchParams.get("sourceType")?.split(',').filter(v => v);
    const dealAmount = searchParams.get("dealAmount");
    const dealStatusArray = searchParams.get("dealStatus")?.split(',').filter(v => v);
    const dealStageArray = searchParams.get("dealStage")?.split(',').filter(v => v);

    if (dealStageArray && dealStageArray.length > 0) {
      whereConditions.dealStage = In(dealStageArray);
    }
    if (dealStatusArray && dealStatusArray.length > 0) {
      whereConditions.dealStatus = In(dealStatusArray);
    }
    if (dealName) {
      whereConditions.dealName = ILike(`%${dealName}%`);
    }
    // Note: sponsorName filtering is handled in query builder since it's in owner table
    // Note: neighborhood and zipCode filtering are handled in query builder since they're in property table

    // Map snake_case column names to camelCase property names
    const columnMapping: { [key: string]: string } = {
      'updated_at': 'updatedAt',
      'created_at': 'createdAt',
      'deal_id': 'dealId',
      'deal_name': 'dealName',
      'deal_stage': 'dealStage',
      'deal_status': 'dealStatus',
      'date_received': 'dateReceived',
      'date_closed': 'dateClosed',
      'date_under_contract': 'dateUnderContract',
      'priority': 'priority',
      'review_status': 'reviewStatus',
      'reviewed_at': 'reviewedAt',
      'extraction_timestamp': 'extractionTimestamp',
      'sponsor_name': 'contactName', // Maps to contact names
      'address': 'address', // Maps to property.address
      'neighborhood': 'neighborhood', // Maps to property.neighborhood
      'zip_code': 'zipcode' // Maps to property.zipcode
    };

    // Build sort options with proper property mapping
    const sortOptions: any = {};
    const mappedSortBy = columnMapping[sortBy] || sortBy;
    const finalSortOrder = sortOrder.toUpperCase();
    
    sortOptions[mappedSortBy] = finalSortOrder;
    
    // Handle capital position and source type filters using query builder
    let total: number;
    let deals: any[];

    if (capitalPosition || sourceType) {
      // Use query builder for NSF field filters
      const queryBuilder = dealsRepository
        .createQueryBuilder('deal')
        .leftJoinAndSelect('deal.property', 'property')
        .leftJoinAndSelect('property.owner', 'owner')
        .leftJoin('deal.nsfFields', 'nsf');

      // Add basic deal filters
      if (search) {
        queryBuilder.andWhere('deal.dealName ILIKE :search', { search: `%${search}%` });
      }
      if (dealStageArray && dealStageArray.length > 0) {
        queryBuilder.andWhere('deal.dealStage IN (:...dealStageArray)', { dealStageArray });
      }
      if (dealStatusArray && dealStatusArray.length > 0) {
        queryBuilder.andWhere('deal.dealStatus IN (:...dealStatusArray)', { dealStatusArray });
      }
      if (dealName) {
        queryBuilder.andWhere('deal.dealName ILIKE :dealName', { dealName: `%${dealName}%` });
      }
      if (sponsorName) {
        // Normalize sponsor name search - handle multiple spaces
        const normalizedSponsorName = sponsorName.replace(/\s+/g, ' ').trim().toLowerCase();
        
        // Join with deal_contacts, contacts, and companies tables to search contact names
        queryBuilder
          .leftJoin('deal_contacts', 'dealContact', 'dealContact.deal_v2_id = deal.dealId AND dealContact.deal_version = :dealVersion', { dealVersion: 'v2' })
          .leftJoin('contacts', 'contact', 'contact.contact_id = dealContact.contact_id')
          .leftJoin('companies', 'company', 'company.company_id = contact.company_id');
        
        queryBuilder.andWhere(
          '(LOWER(TRIM(REGEXP_REPLACE(contact.first_name, \'\\s+\', \' \', \'g\'))) LIKE :sponsorName OR ' +
          'LOWER(TRIM(REGEXP_REPLACE(contact.last_name, \'\\s+\', \' \', \'g\'))) LIKE :sponsorName OR ' +
          'LOWER(TRIM(REGEXP_REPLACE(contact.full_name, \'\\s+\', \' \', \'g\'))) LIKE :sponsorName OR ' +
          'LOWER(TRIM(REGEXP_REPLACE(contact.title, \'\\s+\', \' \', \'g\'))) LIKE :sponsorName OR ' +
          'LOWER(contact.email) LIKE :sponsorName OR ' +
          'LOWER(contact.personal_email) LIKE :sponsorName OR ' +
          'LOWER(TRIM(REGEXP_REPLACE(company.company_name, \'\\s+\', \' \', \'g\'))) LIKE :sponsorName)',
          { 
            sponsorName: `%${normalizedSponsorName}%` 
          }
        );
      }
      if (neighborhood) {
        queryBuilder.andWhere('property.neighborhood ILIKE :neighborhood', { neighborhood: `%${neighborhood}%` });
      }
      if (zipCode) {
        queryBuilder.andWhere('property.zipcode ILIKE :zipCode', { zipCode: `%${zipCode}%` });
      }

      // Add NSF field filters
      if (capitalPosition && capitalPosition.length > 0) {
        queryBuilder.andWhere('nsf.capitalPosition IN (:...capitalPosition)', { capitalPosition });
      }
      if (sourceType && sourceType.length > 0) {
        queryBuilder.andWhere('nsf.sourceType IN (:...sourceType)', { sourceType });
      }
      if (dealAmount) {
        queryBuilder.andWhere('nsf.amount = :dealAmount', { dealAmount: parseFloat(dealAmount) });
      }

      // Get total count
      total = await queryBuilder.getCount();

      // Add pagination and sorting
      // Handle property fields for sorting
      if (['address', 'neighborhood', 'zipcode'].includes(mappedSortBy)) {
        queryBuilder
          .orderBy(`property.${mappedSortBy}`, finalSortOrder as 'ASC' | 'DESC')
          .skip((page - 1) * pageSize)
          .take(pageSize);
      } else if (mappedSortBy === 'contactName') {
        queryBuilder
          .leftJoin('deal_contacts', 'dealContact', 'dealContact.deal_v2_id = deal.dealId AND dealContact.deal_version = :dealVersion', { dealVersion: 'v2' })
          .leftJoin('contacts', 'contact', 'contact.contact_id = dealContact.contact_id')
          .leftJoin('companies', 'company', 'company.company_id = contact.company_id')
          .orderBy(`contact.full_name`, finalSortOrder as 'ASC' | 'DESC')
          .skip((page - 1) * pageSize)
          .take(pageSize);
      } else {
        queryBuilder
          .orderBy(`deal.${mappedSortBy}`, finalSortOrder as 'ASC' | 'DESC')
          .skip((page - 1) * pageSize)
          .take(pageSize);
      }

      deals = await queryBuilder.getMany();
    } else {
      // Use query builder for better performance and field selection
      const queryBuilder = dealsRepository
        .createQueryBuilder('deal')
        .leftJoinAndSelect('deal.property', 'property')
        .leftJoinAndSelect('property.owner', 'owner')
        .select([
          'deal.dealId',
          'deal.dealName',
          'deal.summary',
          'deal.dealStage',
          'deal.dealStatus',
          'deal.dealType',
          'deal.strategy',
          'deal.yieldOnCost',
          'deal.totalProjectCost',
          'deal.askAmount',
          'deal.dateReceived',
          'deal.dateClosed',
          'deal.dateUnderContract',
          'deal.holdPeriod',
          'deal.priority',
          'deal.reviewStatus',
          'deal.reviewedAt',
          'deal.isDistressed',
          'deal.published',
          'deal.llmModelUsed',
          'deal.llmProvider',
          'deal.extractionTimestamp',
          'deal.createdAt',
          'deal.updatedAt',
          'property.propertyId',
          'property.address',
          'property.city',
          'property.state',
          'property.zipcode',
          'property.neighborhood',
          'property.propertyType',
          'property.buildingSqft',
          'property.lotArea',
          'property.yearBuilt',
          'property.numberOfUnits',
          'owner.ownerId',
          'owner.ownerName',
          'owner.entityType'
        ]);

      // Add basic filters
      if (search) {
        queryBuilder.andWhere('deal.dealName ILIKE :search', { search: `%${search}%` });
      }
      if (dealStageArray && dealStageArray.length > 0) {
        queryBuilder.andWhere('deal.dealStage IN (:...dealStageArray)', { dealStageArray });
      }
      if (dealStatusArray && dealStatusArray.length > 0) {
        queryBuilder.andWhere('deal.dealStatus IN (:...dealStatusArray)', { dealStatusArray });
      }
      if (dealName) {
        queryBuilder.andWhere('deal.dealName ILIKE :dealName', { dealName: `%${dealName}%` });
      }
      if (sponsorName) {
        // Normalize sponsor name search - handle multiple spaces
        const normalizedSponsorName = sponsorName.replace(/\s+/g, ' ').trim().toLowerCase();
        
        // Join with deal_contacts, contacts, and companies tables to search contact names
        queryBuilder
          .leftJoin('deal_contacts', 'dealContact', 'dealContact.deal_v2_id = deal.dealId AND dealContact.deal_version = :dealVersion', { dealVersion: 'v2' })
          .leftJoin('contacts', 'contact', 'contact.contact_id = dealContact.contact_id')
          .leftJoin('companies', 'company', 'company.company_id = contact.company_id');
        
        queryBuilder.andWhere(
          '(LOWER(TRIM(REGEXP_REPLACE(contact.first_name, \'\\s+\', \' \', \'g\'))) LIKE :sponsorName OR ' +
          'LOWER(TRIM(REGEXP_REPLACE(contact.last_name, \'\\s+\', \' \', \'g\'))) LIKE :sponsorName OR ' +
          'LOWER(TRIM(REGEXP_REPLACE(contact.full_name, \'\\s+\', \' \', \'g\'))) LIKE :sponsorName OR ' +
          'LOWER(TRIM(REGEXP_REPLACE(contact.title, \'\\s+\', \' \', \'g\'))) LIKE :sponsorName OR ' +
          'LOWER(contact.email) LIKE :sponsorName OR ' +
          'LOWER(contact.personal_email) LIKE :sponsorName OR ' +
          'LOWER(TRIM(REGEXP_REPLACE(company.company_name, \'\\s+\', \' \', \'g\'))) LIKE :sponsorName)',
          { 
            sponsorName: `%${normalizedSponsorName}%` 
          }
        );
      }
      if (neighborhood) {
        queryBuilder.andWhere('property.neighborhood ILIKE :neighborhood', { neighborhood: `%${neighborhood}%` });
      }
      if (zipCode) {
        queryBuilder.andWhere('property.zipcode ILIKE :zipCode', { zipCode: `%${zipCode}%` });
      }

      // Get total count
      total = await queryBuilder.getCount();

      // Add pagination and sorting
      if (['address', 'neighborhood', 'zipcode'].includes(mappedSortBy)) {
        queryBuilder
          .orderBy(`property.${mappedSortBy}`, finalSortOrder as 'ASC' | 'DESC')
          .skip((page - 1) * pageSize)
          .take(pageSize);
      } else if (mappedSortBy === 'contactName') {
        queryBuilder
          .leftJoin('deal_contacts', 'dealContact', 'dealContact.deal_v2_id = deal.dealId AND dealContact.deal_version = :dealVersion', { dealVersion: 'v2' })
          .leftJoin('contacts', 'contact', 'contact.contact_id = dealContact.contact_id')
          .leftJoin('companies', 'company', 'company.company_id = contact.company_id')
          .orderBy(`contact.full_name`, finalSortOrder as 'ASC' | 'DESC')
          .skip((page - 1) * pageSize)
          .take(pageSize);
      } else {
        queryBuilder
          .orderBy(`deal.${mappedSortBy}`, finalSortOrder as 'ASC' | 'DESC')
          .skip((page - 1) * pageSize)
          .take(pageSize);
      }

      deals = await queryBuilder.getMany();
    }

    // Calculate data quality metrics for each deal
    const dealsWithQuality = await Promise.all(
      deals.map(async (deal) => {
        const qualityMetrics = await calculateDealDataQuality(deal, nsfRepository);
        return {
          ...deal,
          data_quality_metrics: qualityMetrics
        };
      })
    );

    const totalPages = Math.ceil(total / pageSize);

    return NextResponse.json({
      deals: dealsWithQuality,
      total,
      totalPages,
      currentPage: page,
      pageSize
    });
  } catch (error) {
    console.error("Error fetching deals v2:", error);
    
    // Provide more specific error messages
    if (error instanceof Error) {
      if (error.message.includes("Connection terminated")) {
        return NextResponse.json(
          { error: "Database connection timeout. Please try again with more specific filters." },
          { status: 504 }
        );
      } else if (error.message.includes("timeout")) {
        return NextResponse.json(
          { error: "Query timeout. Please try with more specific filters or reduce the page size." },
          { status: 504 }
        );
      }
    }
    
    return NextResponse.json(
      { error: "Internal server error", details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}

async function calculateDealDataQuality(deal: any, nsfRepository: any) {
  // Import and use the comprehensive quality calculator for consistency
  const { calculateComprehensiveQuality } = await import('@/lib/utils/comprehensiveQualityCalculator');
  
  // Ensure deal has the required structure for comprehensive calculator
  const dealWithNsfFields = {
    ...deal,
    nsfFields: deal.nsfFields || deal.nsf_fields || [],
  };
  
  const comprehensiveMetrics = calculateComprehensiveQuality(dealWithNsfFields);
  
  // Calculate overall score using the same logic as detail page
  const sections = [
    comprehensiveMetrics.overview,
    comprehensiveMetrics.debt,
    comprehensiveMetrics.equity,
    comprehensiveMetrics.nsf,
    comprehensiveMetrics.property,
    comprehensiveMetrics.financial
  ].filter(section => section.totalFields > 0); // Only include sections with fields
  
  const totalCompletedFields = sections.reduce((sum, section) => sum + section.completedFields, 0);
  const totalPossibleFields = sections.reduce((sum, section) => sum + section.totalFields, 0);
  const qualityScore = totalPossibleFields > 0 ? Math.round((totalCompletedFields / totalPossibleFields) * 100) : 0;
  
  return {
    qualityScore,
    completedFields: totalCompletedFields,
    totalFields: totalPossibleFields,
    missingFields: [] // Comprehensive calculator handles this internally
  };
}

// Export the handler wrapped with TypeORM middleware
export const GET = withTypeORMHandler(getDealsHandler); 