import { NextRequest, NextResponse } from "next/server";
import { withTypeOR<PERSON>Hand<PERSON> } from "@/lib/typeorm/middleware";
import { typeORMService } from "@/lib/typeorm/service";

async function getPropertyTypesHandler(request: NextRequest): Promise<NextResponse> {
  try {
    const query = `
      SELECT DISTINCT 
        value_1 as value, 
        value_1 as label,
        'main' as level
      FROM central_mapping 
      WHERE type = 'Property Type' 
      AND is_active = true 
      AND value_1 IS NOT NULL 
      AND value_1 != ''
      
      UNION ALL
      
      SELECT DISTINCT 
        value_2 as value, 
        value_2 as label,
        'subproperty' as level
      FROM central_mapping 
      WHERE type = 'Property Type' 
      AND is_active = true 
      AND value_2 IS NOT NULL 
      AND value_2 != ''
      
      ORDER BY level DESC, value ASC
    `;
    
    const results = await typeORMService.getDataSource().query(query);
    
    return NextResponse.json(results);
  } catch (error) {
    console.error("Error fetching property types:", error);
    return NextResponse.json(
      { error: "Failed to fetch property types" },
      { status: 500 }
    );
  }
}

export const GET = withType<PERSON><PERSON><PERSON><PERSON><PERSON>(getPropertyTypesHandler);
