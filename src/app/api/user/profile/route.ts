import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth-config'
import { pool } from '@/lib/db'

export async function GET() {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const result = await pool.query(
      'SELECT user_id, username, email, display_name, avatar_url, role, created_at, updated_at FROM users WHERE user_id = $1',
      [parseInt(session.user.id)]
    )

    const user = result.rows[0]
    
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    return NextResponse.json({
      user_id: user.user_id,
      username: user.username,
      email: user.email,
      display_name: user.display_name,
      avatar_url: user.avatar_url,
      role: user.role,
      created_at: user.created_at,
      updated_at: user.updated_at
    })
  } catch (error) {
    console.error('Error fetching user profile:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function PUT(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await req.json()
    const { display_name, avatar_url } = body

    // Validate input
    if (display_name && display_name.length > 255) {
      return NextResponse.json({ error: 'Display name too long' }, { status: 400 })
    }

    // Update user profile
    const result = await pool.query(
      'UPDATE users SET display_name = $1, avatar_url = $2, updated_at = CURRENT_TIMESTAMP WHERE user_id = $3 RETURNING *',
      [display_name, avatar_url, parseInt(session.user.id)]
    )

    if (result.rowCount === 0) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Log the profile update
    await pool.query(
      'INSERT INTO user_edit_logs (user_id, action, details) VALUES ($1, $2, $3)',
      [
        parseInt(session.user.id),
        'profile_update',
        JSON.stringify({
          display_name: display_name,
          avatar_url: avatar_url,
          timestamp: new Date().toISOString()
        })
      ]
    )

    const updatedUser = result.rows[0]

    return NextResponse.json({
      message: 'Profile updated successfully',
      user: {
        user_id: updatedUser.user_id,
        username: updatedUser.username,
        email: updatedUser.email,
        display_name: updatedUser.display_name,
        avatar_url: updatedUser.avatar_url,
        role: updatedUser.role
      }
    })
  } catch (error) {
    console.error('Error updating user profile:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
