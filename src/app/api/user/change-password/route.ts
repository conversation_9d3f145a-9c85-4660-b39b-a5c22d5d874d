import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth-config'
import { pool } from '@/lib/db'
import bcrypt from 'bcryptjs'

export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await req.json()
    const { currentPassword, newPassword } = body

    // Validate input
    if (!currentPassword || !newPassword) {
      return NextResponse.json({ error: 'Current password and new password are required' }, { status: 400 })
    }

    if (newPassword.length < 8) {
      return NextResponse.json({ error: 'New password must be at least 8 characters long' }, { status: 400 })
    }

    // Get current user data
    const userResult = await pool.query(
      'SELECT user_id, email, password_hash FROM users WHERE user_id = $1',
      [parseInt(session.user.id)]
    )

    const user = userResult.rows[0]
    
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Verify current password
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password_hash)
    
    if (!isCurrentPasswordValid) {
      return NextResponse.json({ error: 'Current password is incorrect' }, { status: 400 })
    }

    // Hash new password
    const saltRounds = 12
    const newPasswordHash = await bcrypt.hash(newPassword, saltRounds)

    // Update password in database
    const updateResult = await pool.query(
      'UPDATE users SET password_hash = $1, updated_at = CURRENT_TIMESTAMP WHERE user_id = $2',
      [newPasswordHash, parseInt(session.user.id)]
    )

    if (updateResult.rowCount === 0) {
      return NextResponse.json({ error: 'Failed to update password' }, { status: 500 })
    }

    // Log the password change
    await pool.query(
      'INSERT INTO user_edit_logs (user_id, action, details, ip_address) VALUES ($1, $2, $3, $4)',
      [
        parseInt(session.user.id),
        'password_change',
        JSON.stringify({
          email: user.email,
          timestamp: new Date().toISOString(),
          success: true
        }),
        req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || req.headers.get('cf-connecting-ip') || 'unknown'
      ]
    )

    return NextResponse.json({
      message: 'Password updated successfully'
    })
  } catch (error) {
    console.error('Error changing password:', error)
    
    // Log the failed attempt if we have user ID
    const session = await getServerSession(authOptions)
    if (session?.user?.id) {
      try {
        await pool.query(
          'INSERT INTO user_edit_logs (user_id, action, details, ip_address) VALUES ($1, $2, $3, $4)',
          [
            parseInt(session.user.id),
            'password_change_failed',
            JSON.stringify({
              error: error instanceof Error ? error.message : 'Unknown error',
              timestamp: new Date().toISOString()
            }),
            req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || req.headers.get('cf-connecting-ip') || 'unknown'
          ]
        )
      } catch (logError) {
        console.error('Error logging failed password change:', logError)
      }
    }

    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
