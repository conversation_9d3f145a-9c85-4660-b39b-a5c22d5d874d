import { NextRequest, NextResponse } from "next/server";
import { typeORMService } from "@/lib/typeorm/service";
import { withTypeOR<PERSON><PERSON>and<PERSON> } from "@/lib/typeorm/middleware";
import { Like, Between, In, FindOptionsWhere, FindManyOptions, ILike } from "typeorm";
import { DealsV2 } from "@/lib/typeorm/entities/DealsV2";
import { createMultiFieldSearchPatterns } from "@/lib/utils/searchNormalization";

async function searchDealsHandler(request: NextRequest): Promise<NextResponse> {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const pageSize = parseInt(searchParams.get("pageSize") || "20");
    const search = searchParams.get("search") || "";
    const sortBy = searchParams.get("sortBy") || "updatedAt";
    const sortOrder = searchParams.get("sortOrder") || "DESC";
    
    // Filter parameters
    const dealType = searchParams.get("dealType");
    const dealStage = searchParams.get("dealStage")?.split(',').filter(v => v);
    const dealStatus = searchParams.get("dealStatus")?.split(',').filter(v => v);
    const strategy = searchParams.get("strategy")?.split(',').filter(v => v);
    const propertyType = searchParams.get("propertyType")?.split(',').filter(v => v);
    const state = searchParams.get("state")?.split(',').filter(v => v);
    const city = searchParams.get("city")?.split(',').filter(v => v);
    const amount = searchParams.get("amount");
    const isDistressed = searchParams.get("isDistressed");
    const reviewStatus = searchParams.get("reviewStatus");
    
    // Enhanced search parameters with normalization and URL decoding
    const dealName = searchParams.get("dealName") ? decodeURIComponent(searchParams.get("dealName")!).trim() : undefined;
    const sponsorName = searchParams.get("sponsorName") ? decodeURIComponent(searchParams.get("sponsorName")!).trim() : undefined;
    const address = searchParams.get("address") ? decodeURIComponent(searchParams.get("address")!).trim() : undefined;
    const neighborhood = searchParams.get("neighborhood") ? decodeURIComponent(searchParams.get("neighborhood")!).trim() : undefined;
    const zipCode = searchParams.get("zipCode") ? decodeURIComponent(searchParams.get("zipCode")!).trim() : undefined;
    const capitalPosition = searchParams.get("capitalPosition")?.split(',').filter(v => v);
    const sourceType = searchParams.get("sourceType")?.split(',').filter(v => v);
    const published = searchParams.get("published");

    // Get repositories from the service
    const dealsRepository = typeORMService.getDealsRepository();
    const propertyRepository = typeORMService.getPropertyRepository();
    const nsfRepository = typeORMService.getNsfRepository();

    // Build search conditions
    const whereConditions: FindOptionsWhere<DealsV2> = {};
    
    // Enhanced search conditions (case insensitive) - only for fields that exist on DealsV2 entity
    if (dealName) {
      whereConditions.dealName = ILike(`%${dealName}%`);
    }
    // Note: sponsorName, neighborhood, zipCode are in the property relation, handled in query builder
    
    if (dealType) {
      whereConditions.dealType = dealType;
    }
    
    if (dealStage && dealStage.length > 0) {
      whereConditions.dealStage = In(dealStage);
    }
    
    if (dealStatus && dealStatus.length > 0) {
      whereConditions.dealStatus = In(dealStatus);
    }
    
    if (strategy && strategy.length > 0) {
      whereConditions.strategy = In(strategy);
    }
    
    if (isDistressed !== null && isDistressed !== undefined) {
      whereConditions.isDistressed = isDistressed === "true";
    }
    
    if (reviewStatus) {
      whereConditions.reviewStatus = reviewStatus;
    }
    
    if (published !== null && published !== undefined) {
      whereConditions.published = published === "true";
    }

    // Map snake_case column names to camelCase property names
    const columnMapping: { [key: string]: string } = {
      'updated_at': 'updatedAt',
      'created_at': 'createdAt',
      'deal_id': 'dealId',
      'deal_name': 'dealName',
      'deal_stage': 'dealStage',
      'deal_status': 'dealStatus',
      'deal_type': 'dealType',
      'date_received': 'dateReceived',
      'date_closed': 'dateClosed',
      'date_under_contract': 'dateUnderContract',
      'priority': 'priority',
      'review_status': 'reviewStatus',
      'reviewed_at': 'reviewedAt',
      'extraction_timestamp': 'extractionTimestamp',
      'sponsor_name': 'contactName', // Maps to contact names
      'address': 'address', // Maps to property.address
      'neighborhood': 'neighborhood', // Maps to property.neighborhood
      'zip_code': 'zipcode', // Maps to property.zipcode
      'ask_amount': 'askAmount', // Maps to askAmount array in dealsv2
      'capital_position': 'askCapitalPosition', // Maps to askCapitalPosition array in dealsv2
      'city': 'city', // Maps to property.city
      'strategy': 'strategy', // Maps to strategy in dealsv2
      'property_type': 'propertyType', // Maps to property.propertyType
      'total_project_cost': 'totalProjectCost' // Maps to totalProjectCost in dealsv2
    };

    // Build sort options with proper property mapping
    const sortOptions: any = {};
    const mappedSortBy = columnMapping[sortBy] || sortBy;
    const finalSortOrder = sortOrder.toUpperCase();
    
    sortOptions[mappedSortBy] = finalSortOrder;

    // Handle general search with property fields
    let total: number;
    let deals: any[];

    if (search) {
      // Create forgiving search patterns
      const searchPatterns = createMultiFieldSearchPatterns(search);
      
      // Use raw SQL for search to handle contacts and companies tables that don't have entities
      const publishedFilter = published !== null && published !== undefined ? 'AND d.published = $4' : '';
      
      // Build additional filters
      let additionalFilters = '';
      let paramIndex = 4; // Start after search patterns and published filter
      
      if (published !== null && published !== undefined) paramIndex++;
      
      if (capitalPosition && capitalPosition.length > 0) {
        additionalFilters += ` AND EXISTS (
          SELECT 1 FROM deal_nsf_fields nsf2 
          WHERE nsf2.deal_id = d.deal_id 
          AND nsf2.is_required = true 
          AND nsf2.nsf_context = 'sources' 
          AND nsf2.source_type = ANY($${paramIndex})
        )`;
        paramIndex++;
      }
      
      if (strategy && strategy.length > 0) {
        additionalFilters += ` AND d.strategy = ANY($${paramIndex})`;
        paramIndex++;
      }
      
      if (propertyType && propertyType.length > 0) {
        additionalFilters += ` AND p.property_type = ANY($${paramIndex})`;
        paramIndex++;
      }
      
      if (state && state.length > 0) {
        additionalFilters += ` AND p.state = ANY($${paramIndex})`;
        paramIndex++;
      }
      
      if (city && city.length > 0) {
        additionalFilters += ` AND p.city = ANY($${paramIndex})`;
        paramIndex++;
      }
      
      if (amount) {
        additionalFilters += ` AND EXISTS (
          SELECT 1 FROM deal_nsf_fields nsf3 
          WHERE nsf3.deal_id = d.deal_id 
          AND nsf3.is_required = true 
          AND nsf3.nsf_context = 'sources' 
          AND nsf3.amount IS NOT NULL
          AND nsf3.amount = $${paramIndex}
        )`;
        paramIndex++;
      }
      // Map frontend sort fields to actual database column names
      const dbColumnMapping: { [key: string]: string } = {
      'updatedAt': 'updated_at',
      'createdAt': 'created_at',
      'dealId': 'deal_id',
      'dealName': 'deal_name',
      'dealStage': 'deal_stage',
      'dealStatus': 'deal_status',
        'dealType': 'deal_type',
      'dateReceived': 'date_received',
      'dateClosed': 'date_closed',
      'dateUnderContract': 'date_under_contract',
      'priority': 'priority',
      'reviewStatus': 'review_status',
      'reviewedAt': 'reviewed_at',
      'extractionTimestamp': 'extraction_timestamp',
        'yieldOnCost': 'yield_on_cost',
        'totalProjectCost': 'total_project_cost',
        'holdPeriod': 'hold_period',
        'isDistressed': 'is_distressed',
        'llmModelUsed': 'llm_model_used',
        'llmProvider': 'llm_provider',
        'strategy': 'strategy'
      };
      
      // Whitelist of allowed sort fields to prevent SQL injection
      const allowedSortFields = new Set([
        'deal_id', 'deal_name', 'deal_stage', 'deal_status', 'deal_type', 'strategy',
        'yield_on_cost', 'total_project_cost', 'date_received', 'date_closed', 
        'date_under_contract', 'hold_period', 'priority', 'review_status', 
        'reviewed_at', 'is_distressed', 'published', 'llm_model_used', 
        'llm_provider', 'extraction_timestamp', 'created_at', 'updated_at',
        'address', 'neighborhood', 'zipcode', 'city', 'property_type',
        'building_sqft', 'lot_area', 'year_built', 'number_of_units',
        'owner_id', 'owner_name', 'entity_type', 'full_name'
      ]);
      
      // Validate sort field to prevent SQL injection
      let sortField: string;
      if (mappedSortBy === 'askAmount') {
        sortField = 'nsf.amount';
      } else if (mappedSortBy === 'askCapitalPosition') {
        sortField = '"sortCapitalPosition"';
      } else if (['address', 'neighborhood', 'zipcode', 'city', 'propertyType'].includes(mappedSortBy)) {
        const propertyField = mappedSortBy === 'propertyType' ? 'property_type' : mappedSortBy;
        if (allowedSortFields.has(propertyField)) {
          sortField = `p.${propertyField}`;
        } else {
          sortField = 'd.updated_at'; // fallback
        }
      } else if (mappedSortBy === 'contactName') {
        sortField = 'c.full_name';
      } else {
        const dbField = dbColumnMapping[mappedSortBy] || mappedSortBy;
        if (allowedSortFields.has(dbField)) {
          sortField = `d.${dbField}`;
        } else {
          sortField = 'd.updated_at'; // fallback
        }
      }
      
      // Validate sort order to prevent SQL injection
      const allowedSortOrders = new Set(['ASC', 'DESC', 'asc', 'desc']);
      const finalSortOrder = allowedSortOrders.has(sortOrder) ? sortOrder.toUpperCase() : 'DESC';
      
      const query = `
        SELECT DISTINCT
          d.deal_id as "dealId",
          d.deal_name as "dealName",
          d.summary,
          d.deal_stage as "dealStage",
          d.deal_status as "dealStatus",
          d.deal_type as "dealType",
          d.strategy,
          d.yield_on_cost as "yieldOnCost",
          d.total_project_cost as "totalProjectCost",
          d.date_received as "dateReceived",
          d.date_closed as "dateClosed",
          d.date_under_contract as "dateUnderContract",
          d.hold_period as "holdPeriod",
          d.priority,
          d.review_status as "reviewStatus",
          d.reviewed_at as "reviewedAt",
          d.is_distressed as "isDistressed",
          d.published,
          d.llm_model_used as "llmModelUsed",
          d.llm_provider as "llmProvider",
          d.extraction_timestamp as "extractionTimestamp",
          d.created_at as "createdAt",
          d.updated_at as "updatedAt",
          p.property_id as "propertyId",
          p.address,
          p.city,
          p.state,
          p.zipcode,
          p.neighborhood,
          p.property_type as "propertyType",
          p.building_sqft as "buildingSqft",
          p.lot_area as "lotArea",
          p.year_built as "yearBuilt",
          p.number_of_units as "numberOfUnits",
          o.owner_id as "ownerId",
          o.owner_name as "ownerName",
          o.entity_type as "entityType"${mappedSortBy === 'contactName' ? ',\n          c.full_name as "contactName"' : ''}${mappedSortBy === 'askCapitalPosition' ? ',\n          (SELECT nsf2.source_type FROM deal_nsf_fields nsf2 \n            WHERE nsf2.deal_id = d.deal_id AND nsf2.is_required = true \n            AND nsf2.nsf_context = \'sources\' \n            ORDER BY nsf2.source_type LIMIT 1) as "sortCapitalPosition"' : ''},
          -- Get required capital positions and amounts from deal_nsf_fields
          COALESCE(
            ARRAY_AGG(
              CASE 
                WHEN nsf.is_required = true AND nsf.nsf_context = 'sources' 
                THEN nsf.source_type 
                ELSE NULL 
              END
            ) FILTER (WHERE nsf.is_required = true AND nsf.nsf_context = 'sources'),
            ARRAY[]::text[]
          ) as "askCapitalPosition",
          COALESCE(
            ARRAY_AGG(
              CASE 
                WHEN nsf.is_required = true AND nsf.nsf_context = 'sources' 
                THEN nsf.amount 
                ELSE NULL 
              END
            ) FILTER (WHERE nsf.is_required = true AND nsf.nsf_context = 'sources'),
            ARRAY[]::numeric[]
          ) as "askAmount"
        FROM dealsv2 d
        LEFT JOIN properties p ON d.property_id = p.property_id
        LEFT JOIN owners o ON p.owner_id = o.owner_id
        LEFT JOIN deal_nsf_fields nsf ON d.deal_id = nsf.deal_id
        LEFT JOIN deal_contacts dc ON dc.deal_v2_id = d.deal_id AND dc.deal_version = 'v2'
        LEFT JOIN contacts c ON c.contact_id = dc.contact_id
        LEFT JOIN companies comp ON comp.company_id = c.company_id
        WHERE (
          d.deal_name ILIKE $1 OR 
          d.summary ILIKE $1 OR 
          p.address ILIKE $1 OR 
          p.city ILIKE $1 OR 
          p.state ILIKE $1 OR 
          p.zipcode ILIKE $1 OR 
          p.region ILIKE $1 OR 
          c.full_name ILIKE $1 OR 
          c.first_name ILIKE $1 OR 
          c.last_name ILIKE $1 OR 
          c.email ILIKE $2 OR 
          c.personal_email ILIKE $2 OR 
          comp.company_name ILIKE $3
        ) ${publishedFilter}
        GROUP BY d.deal_id, d.deal_name, d.summary, d.deal_stage, d.deal_status, d.deal_type, d.strategy,
                 d.yield_on_cost, d.total_project_cost, d.date_received, d.date_closed, d.date_under_contract,
                 d.hold_period, d.priority, d.review_status, d.reviewed_at, d.is_distressed, d.published,
                 d.llm_model_used, d.llm_provider, d.extraction_timestamp, d.created_at, d.updated_at,
                 p.property_id, p.address, p.city, p.state, p.zipcode, p.neighborhood, p.property_type,
                 p.building_sqft, p.lot_area, p.year_built, p.number_of_units,
                 o.owner_id, o.owner_name, o.entity_type
        ORDER BY ${sortField} ${finalSortOrder}
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `;
      
      const countQuery = `
        SELECT COUNT(DISTINCT d.deal_id) as total
        FROM dealsv2 d
        LEFT JOIN properties p ON d.property_id = p.property_id
        LEFT JOIN owners o ON p.owner_id = o.owner_id
        LEFT JOIN deal_nsf_fields nsf ON d.deal_id = nsf.deal_id
        LEFT JOIN deal_contacts dc ON dc.deal_v2_id = d.deal_id AND dc.deal_version = 'v2'
        LEFT JOIN contacts c ON c.contact_id = dc.contact_id
        LEFT JOIN companies comp ON comp.company_id = c.company_id
        WHERE (
          d.deal_name ILIKE $1 OR 
          d.summary ILIKE $1 OR 
          p.address ILIKE $1 OR 
          p.city ILIKE $1 OR 
          p.state ILIKE $1 OR 
          p.zipcode ILIKE $1 OR 
          p.region ILIKE $1 OR 
          c.full_name ILIKE $1 OR 
          c.first_name ILIKE $1 OR 
          c.last_name ILIKE $1 OR 
          c.email ILIKE $2 OR 
          c.personal_email ILIKE $2 OR 
          comp.company_name ILIKE $3
        ) ${publishedFilter}
      `;
      
      const baseParams = [
        searchPatterns.base,
        searchPatterns.email,
        searchPatterns.company,
        ...(published !== null && published !== undefined ? [published === "true"] : []),
        ...(capitalPosition && capitalPosition.length > 0 ? [capitalPosition] : []),
        ...(strategy && strategy.length > 0 ? [strategy] : []),
        ...(propertyType && propertyType.length > 0 ? [propertyType] : []),
        ...(state && state.length > 0 ? [state] : []),
        ...(city && city.length > 0 ? [city] : []),
        ...(amount ? [parseFloat(amount)] : [])
      ];
      
      const queryParams = [...baseParams, pageSize, (page - 1) * pageSize];
      
      // Get total count
      const countResult = await typeORMService.getDataSource().query(countQuery, baseParams);
      total = parseInt(countResult[0].total);
      
      // Get deals
      deals = await typeORMService.getDataSource().query(query, queryParams);
    } else {
      // Use raw SQL query to properly aggregate NSF fields
      const publishedFilter = published !== null && published !== undefined ? 'AND d.published = $1' : '';
      // Map frontend sort fields to actual database column names
      const dbColumnMapping: { [key: string]: string } = {
        'updatedAt': 'updated_at',
        'createdAt': 'created_at',
        'dealId': 'deal_id',
        'dealName': 'deal_name',
        'dealStage': 'deal_stage',
        'dealStatus': 'deal_status',
        'dealType': 'deal_type',
        'dateReceived': 'date_received',
        'dateClosed': 'date_closed',
        'dateUnderContract': 'date_under_contract',
        'priority': 'priority',
        'reviewStatus': 'review_status',
        'reviewedAt': 'reviewed_at',
        'extractionTimestamp': 'extraction_timestamp',
        'yieldOnCost': 'yield_on_cost',
        'totalProjectCost': 'total_project_cost',
        'holdPeriod': 'hold_period',
        'isDistressed': 'is_distressed',
        'llmModelUsed': 'llm_model_used',
        'llmProvider': 'llm_provider',
        'strategy': 'strategy'
      };
      
      const sortField = mappedSortBy === 'askAmount' ? 'nsf.amount' : 
                       mappedSortBy === 'askCapitalPosition' ? '"sortCapitalPosition"' :
                       ['address', 'neighborhood', 'zipcode', 'city', 'propertyType'].includes(mappedSortBy) ? 
                         `p.${mappedSortBy === 'propertyType' ? 'property_type' : mappedSortBy}` :
                       mappedSortBy === 'contactName' ? 'c.full_name' :
                       `d.${dbColumnMapping[mappedSortBy] || mappedSortBy}`;
      
      // Add contacts join when sorting by contactName
      const contactsJoin = mappedSortBy === 'contactName' ? 
        `LEFT JOIN deal_contacts dc ON dc.deal_v2_id = d.deal_id AND dc.deal_version = 'v2'
         LEFT JOIN contacts c ON c.contact_id = dc.contact_id` : '';
      
      // Build additional filters for non-search path
      let additionalFilters = '';
      let paramIndex = 1;
      
      if (published !== null && published !== undefined) paramIndex++;
      
      if (capitalPosition && capitalPosition.length > 0) {
        additionalFilters += ` AND EXISTS (
          SELECT 1 FROM deal_nsf_fields nsf2 
          WHERE nsf2.deal_id = d.deal_id 
          AND nsf2.is_required = true 
          AND nsf2.nsf_context = 'sources' 
          AND nsf2.source_type = ANY($${paramIndex})
        )`;
        paramIndex++;
      }
      
      if (strategy && strategy.length > 0) {
        additionalFilters += ` AND d.strategy = ANY($${paramIndex})`;
        paramIndex++;
      }
      
      if (propertyType && propertyType.length > 0) {
        additionalFilters += ` AND p.property_type = ANY($${paramIndex})`;
        paramIndex++;
      }
      
      if (state && state.length > 0) {
        additionalFilters += ` AND p.state = ANY($${paramIndex})`;
      paramIndex++;
    }

      if (city && city.length > 0) {
        additionalFilters += ` AND p.city = ANY($${paramIndex})`;
        paramIndex++;
      }
      
      if (amount) {
        additionalFilters += ` AND EXISTS (
          SELECT 1 FROM deal_nsf_fields nsf3 
          WHERE nsf3.deal_id = d.deal_id 
          AND nsf3.is_required = true 
          AND nsf3.nsf_context = 'sources' 
          AND nsf3.amount IS NOT NULL
          AND nsf3.amount = $${paramIndex}
        )`;
        paramIndex++;
      }

      const query = `
        SELECT DISTINCT
          d.deal_id as "dealId",
          d.deal_name as "dealName",
          d.summary,
          d.deal_stage as "dealStage",
          d.deal_status as "dealStatus",
          d.deal_type as "dealType",
          d.strategy,
          d.yield_on_cost as "yieldOnCost",
          d.total_project_cost as "totalProjectCost",
          d.date_received as "dateReceived",
          d.date_closed as "dateClosed",
          d.date_under_contract as "dateUnderContract",
          d.hold_period as "holdPeriod",
          d.priority,
          d.review_status as "reviewStatus",
          d.reviewed_at as "reviewedAt",
          d.is_distressed as "isDistressed",
          d.published,
          d.llm_model_used as "llmModelUsed",
          d.llm_provider as "llmProvider",
          d.extraction_timestamp as "extractionTimestamp",
          d.created_at as "createdAt",
          d.updated_at as "updatedAt",
          p.property_id as "propertyId",
          p.address,
          p.city,
          p.state,
          p.zipcode,
          p.neighborhood,
          p.property_type as "propertyType",
          p.building_sqft as "buildingSqft",
          p.lot_area as "lotArea",
          p.year_built as "yearBuilt",
          p.number_of_units as "numberOfUnits",
          o.owner_id as "ownerId",
          o.owner_name as "ownerName",
          o.entity_type as "entityType"${mappedSortBy === 'contactName' ? ',\n          c.full_name as "contactName"' : ''}${mappedSortBy === 'askCapitalPosition' ? ',\n          (SELECT nsf2.source_type FROM deal_nsf_fields nsf2 \n            WHERE nsf2.deal_id = d.deal_id AND nsf2.is_required = true \n            AND nsf2.nsf_context = \'sources\' \n            ORDER BY nsf2.source_type LIMIT 1) as "sortCapitalPosition"' : ''},
          -- Get required capital positions and amounts from deal_nsf_fields
          COALESCE(
            ARRAY_AGG(
              CASE 
                WHEN nsf.is_required = true AND nsf.nsf_context = 'sources' 
                THEN nsf.source_type 
                ELSE NULL 
              END
            ) FILTER (WHERE nsf.is_required = true AND nsf.nsf_context = 'sources'),
            ARRAY[]::text[]
          ) as "askCapitalPosition",
          COALESCE(
            ARRAY_AGG(
              CASE 
                WHEN nsf.is_required = true AND nsf.nsf_context = 'sources' 
                THEN nsf.amount 
                ELSE NULL 
              END
            ) FILTER (WHERE nsf.is_required = true AND nsf.nsf_context = 'sources'),
            ARRAY[]::numeric[]
          ) as "askAmount"
        FROM dealsv2 d
        LEFT JOIN properties p ON d.property_id = p.property_id
        LEFT JOIN owners o ON p.owner_id = o.owner_id
        LEFT JOIN deal_nsf_fields nsf ON d.deal_id = nsf.deal_id
        ${contactsJoin}
        WHERE 1=1 ${publishedFilter}${additionalFilters}
        GROUP BY d.deal_id, d.deal_name, d.summary, d.deal_stage, d.deal_status, d.deal_type, d.strategy,
                 d.yield_on_cost, d.total_project_cost, d.date_received, d.date_closed, d.date_under_contract,
                 d.hold_period, d.priority, d.review_status, d.reviewed_at, d.is_distressed, d.published,
                 d.llm_model_used, d.llm_provider, d.extraction_timestamp, d.created_at, d.updated_at,
                 p.property_id, p.address, p.city, p.state, p.zipcode, p.neighborhood, p.property_type,
                 p.building_sqft, p.lot_area, p.year_built, p.number_of_units,
                 o.owner_id, o.owner_name, o.entity_type${mappedSortBy === 'contactName' ? ', c.full_name' : ''}
        ORDER BY ${sortField} ${finalSortOrder}
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `;

      const countQuery = `
        SELECT COUNT(DISTINCT d.deal_id) as total
        FROM dealsv2 d
        LEFT JOIN properties p ON d.property_id = p.property_id
        LEFT JOIN owners o ON p.owner_id = o.owner_id
        LEFT JOIN deal_nsf_fields nsf ON d.deal_id = nsf.deal_id
        ${contactsJoin}
        WHERE 1=1 ${publishedFilter}${additionalFilters}
      `;
      
      const baseParams = [
        ...(published !== null && published !== undefined ? [published === "true"] : []),
        ...(capitalPosition && capitalPosition.length > 0 ? [capitalPosition] : []),
        ...(strategy && strategy.length > 0 ? [strategy] : []),
        ...(propertyType && propertyType.length > 0 ? [propertyType] : []),
        ...(state && state.length > 0 ? [state] : []),
        ...(city && city.length > 0 ? [city] : []),
        ...(amount ? [parseFloat(amount)] : [])
      ];
      
      const queryParams = [...baseParams, pageSize, (page - 1) * pageSize];
      
      // Get total count
      const countResult = await typeORMService.getDataSource().query(countQuery, baseParams);
      total = parseInt(countResult[0].total);
      
      // Get dealsersnot popu
      deals = await typeORMService.getDataSource().query(query, queryParams);
    }

    // Amount filtering is now handled in SQL queries above

    // Calculate data quality metrics for each deal
    const dealsWithQuality = await Promise.all(
      deals.map(async (deal) => {
        const qualityMetrics = await calculateDealDataQuality(deal, nsfRepository);
      return {
        ...deal,
        data_quality_metrics: qualityMetrics
      };
      })
    );

    const totalPages = Math.ceil(total / pageSize);

    return NextResponse.json({
      deals: dealsWithQuality,
      total,
      totalPages,
      currentPage: page,
      pageSize,
      filters: {
        dealStage,
        dealStatus,
        strategy,
        propertyType,
        capitalPosition,
        state,
        city,
        amount: amount ? parseFloat(amount) : null,
        isDistressed,
        reviewStatus,
        dealName,
        sponsorName,
        address,
        neighborhood,
        zipCode,
        published
      }
    });
  } catch (error) {
    console.error("Error searching deals:", error);
    
    // Provide more specific error messages
    if (error instanceof Error) {
      if (error.message.includes("Connection terminated")) {
        return NextResponse.json(
          { error: "Database connection timeout. Please try again with more specific filters." },
          { status: 504 }
        );
      } else if (error.message.includes("timeout")) {
        return NextResponse.json(
          { error: "Query timeout. Please try with more specific filters or reduce the page size." },
          { status: 504 }
        );
      }
    }
    
    return NextResponse.json(
      { error: "Internal server error", details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}

// Helper function to calculate deal data quality
async function calculateDealDataQuality(deal: any, nsfRepository: any) {
  const fields = [
    'dealName', 'summary', 'dealStage', 'dealType', 'strategy', 'dealStatus',
    'yieldOnCost', 'commonEquityInternalRateOfReturnIrr', 'commonEquityEquityMultiple',
    'gpEquityMultiple', 'gpInternalRateOfReturnIrr', 'lpEquityMultiple', 'lpInternalRateOfReturnIrr',
    'totalEquityMultiple', 'totalInternalRateOfReturnIrr', 'holdPeriod', 'askAmount'
  ];

  let completedFields = 0;
  const missingFields: string[] = [];

  fields.forEach(field => {
    const value = deal[field];
    const hasValue = value !== null && value !== '' && value !== undefined && 
                    (!Array.isArray(value) || value.length > 0);
    
    if (hasValue) {
      completedFields++;
    } else {
      missingFields.push(field);
    }
  });

  const qualityScore = Math.round((completedFields / fields.length) * 100);

  return {
    qualityScore,
    completedFields,
    totalFields: fields.length,
    missingFields
  };
}

export const GET = withTypeORMHandler(searchDealsHandler);