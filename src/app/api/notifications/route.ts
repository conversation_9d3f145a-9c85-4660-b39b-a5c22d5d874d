import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth-config'
import { pool } from '@/lib/db'

export async function GET() {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const result = await pool.query(
      `SELECT id, title, message, type, is_read, created_at, read_at 
       FROM user_notifications 
       WHERE user_id = $1 
       ORDER BY created_at DESC 
       LIMIT 50`,
      [parseInt(session.user.id)]
    )

    const notifications = result.rows.map(row => ({
      id: row.id,
      title: row.title,
      message: row.message,
      type: row.type,
      is_read: row.is_read,
      created_at: row.created_at,
      read_at: row.read_at
    }))

    return NextResponse.json({ notifications })
  } catch (error) {
    console.error('Error fetching notifications:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    // Check if user is admin (only admins can create notifications for now)
    if (!session?.user?.id || session.user.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await req.json()
    const { title, message, type = 'info', user_id } = body

    // Validate input
    if (!title || !message) {
      return NextResponse.json({ error: 'Title and message are required' }, { status: 400 })
    }

    if (!['info', 'warning', 'error', 'success'].includes(type)) {
      return NextResponse.json({ error: 'Invalid notification type' }, { status: 400 })
    }

    // If user_id is provided, send to specific user, otherwise send to all users
    let result
    if (user_id) {
      result = await pool.query(
        'INSERT INTO user_notifications (user_id, title, message, type) VALUES ($1, $2, $3, $4) RETURNING *',
        [user_id, title, message, type]
      )
    } else {
      // Send to all users
      const usersResult = await pool.query('SELECT user_id FROM users')
      const userIds = usersResult.rows.map(row => row.user_id)
      
      const insertPromises = userIds.map(userId =>
        pool.query(
          'INSERT INTO user_notifications (user_id, title, message, type) VALUES ($1, $2, $3, $4)',
          [userId, title, message, type]
        )
      )
      
      await Promise.all(insertPromises)
      result = { rowCount: userIds.length }
    }

    return NextResponse.json({
      message: 'Notification(s) created successfully',
      count: result.rowCount
    })
  } catch (error) {
    console.error('Error creating notification:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
