import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth-config'
import { pool } from '@/lib/db'

export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await req.json()
    const { notification_id, mark_all = false } = body

    if (mark_all) {
      // Mark all notifications as read for this user
      await pool.query(
        'UPDATE user_notifications SET is_read = TRUE, read_at = CURRENT_TIMESTAMP WHERE user_id = $1 AND is_read = FALSE',
        [parseInt(session.user.id)]
      )

      return NextResponse.json({ message: 'All notifications marked as read' })
    } else if (notification_id) {
      // Mark specific notification as read
      const result = await pool.query(
        'UPDATE user_notifications SET is_read = TRUE, read_at = CURRENT_TIMESTAMP WHERE id = $1 AND user_id = $2 AND is_read = FALSE RETURNING *',
        [notification_id, parseInt(session.user.id)]
      )

      if (result.rowCount === 0) {
        return NextResponse.json({ error: 'Notification not found or already read' }, { status: 404 })
      }

      return NextResponse.json({ 
        message: 'Notification marked as read',
        notification: result.rows[0]
      })
    } else {
      return NextResponse.json({ error: 'notification_id is required unless mark_all is true' }, { status: 400 })
    }
  } catch (error) {
    console.error('Error marking notification as read:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
