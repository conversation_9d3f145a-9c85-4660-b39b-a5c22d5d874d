import { NextRequest, NextResponse } from 'next/server'
import { DataNormalizationService } from '@/lib/services/dataNormalizationService'

interface NormalizeRequest {
  type: 'company' | 'contact' | 'both'
  batchSize?: number
  incremental?: boolean // Only process new/updated records
}

interface NormalizeResponse {
  success: boolean
  companies?: {
    processed: number
    errors: number
  }
  contacts?: {
    processed: number
    errors: number
  }
  processingTime: number
  error?: string
}

export async function POST(request: NextRequest) {
  const startTime = Date.now()
  
  try {
    const { type, batchSize = 500, incremental = true }: NormalizeRequest = await request.json()

    if (!type || !['company', 'contact', 'both'].includes(type)) {
      return NextResponse.json(
        { error: 'Invalid type. Must be "company", "contact", or "both"' },
        { status: 400 }
      )
    }

    const response: NormalizeResponse = {
      success: true,
      processingTime: 0
    }

    // Normalize companies
    if (type === 'company' || type === 'both') {
      console.log(`Normalizing company data (batch size: ${batchSize}, incremental: ${incremental})...`)
      const companyResults = await DataNormalizationService.normalizeAllCompanies(batchSize)
      response.companies = companyResults
      console.log(`Company normalization completed: ${companyResults.processed} processed, ${companyResults.errors} errors`)
    }

    // Normalize contacts
    if (type === 'contact' || type === 'both') {
      console.log(`Normalizing contact data (batch size: ${batchSize}, incremental: ${incremental})...`)
      const contactResults = await DataNormalizationService.normalizeAllContacts(batchSize)
      response.contacts = contactResults
      console.log(`Contact normalization completed: ${contactResults.processed} processed, ${contactResults.errors} errors`)
    }

    response.processingTime = Date.now() - startTime

    return NextResponse.json(response)

  } catch (error) {
    console.error('Error in data normalization:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        success: false,
        processingTime: Date.now() - startTime
      },
      { status: 500 }
    )
  }
}

// GET endpoint to check normalization status
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const type = searchParams.get('type') // 'company' | 'contact' | 'both'

    const { pool } = await import('@/lib/db')
    const client = await pool.connect()

    try {
      const stats: any = {}

      if (!type || type === 'company' || type === 'both') {
        // Get company normalization stats
        const companyStats = await client.query(`
          SELECT 
            COUNT(*) as total_companies,
            (SELECT COUNT(*) FROM company_normalized_data) as normalized_companies,
            (SELECT COUNT(*) FROM company_normalized_data WHERE updated_at > NOW() - INTERVAL '1 hour') as recently_updated
          FROM companies
        `)
        stats.companies = companyStats.rows[0]
      }

      if (!type || type === 'contact' || type === 'both') {
        // Get contact normalization stats
        const contactStats = await client.query(`
          SELECT 
            COUNT(*) as total_contacts,
            (SELECT COUNT(*) FROM contact_normalized_data) as normalized_contacts,
            (SELECT COUNT(*) FROM contact_normalized_data WHERE updated_at > NOW() - INTERVAL '1 hour') as recently_updated
          FROM contacts
        `)
        stats.contacts = contactStats.rows[0]
      }

      return NextResponse.json({
        success: true,
        stats
      })

    } finally {
      client.release()
    }

  } catch (error) {
    console.error('Error getting normalization status:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
