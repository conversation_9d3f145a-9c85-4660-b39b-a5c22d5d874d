import { BaseProcessor } from './BaseProcessor'
import { ProcessorOptions, UnifiedEntityData } from '../../types/processing'
import { LLMFactory, LLMMessage, createProcessorLoggerAdapter, LogLevel } from '../llm'
import { BaseScraper } from '../scrapers/BaseScraper'
import { COMPANY_OVERVIEW_V2_SYSTEM_PROMPT, COMPANY_OVERVIEW_V2_USER_TEMPLATE_FUNCTION } from '../prompts/company-overview-v2'

// Enhanced data model to match the new comprehensive schema exactly
interface CompanyOverviewDataV2 {
  // Core Company Information
  company_name?: string
  company_type?: string
  company_industry?: string
  business_model?: string
  founded_year?: number
  
  // Investment & Strategy
  investment_focus?: string[]
  investment_strategy_mission?: string
  investment_strategy_approach?: string
  
  // Contact Information (AI-updated fields)
  main_phone?: string
  secondary_phone?: string
  main_email?: string
  secondary_email?: string
  linkedin?: string
  twitter?: string
  facebook?: string
  instagram?: string
  youtube?: string
  
  // Address Information (AI-updated headquarters fields)
  headquarters_address?: string
  headquarters_city?: string
  headquarters_state?: string
  headquarters_zipcode?: string
  headquarters_country?: string
  additional_address?: string
  additional_city?: string
  additional_state?: string
  additional_zipcode?: string
  additional_country?: string
  office_locations?: string[]
  
  // Company Metrics
  fund_size?: number
  aum?: number
  number_of_properties?: number
  number_of_offices?: number
  number_of_employees?: number
  annual_revenue?: number
  net_income?: number
  ebitda?: number
  profit_margin?: number
  market_capitalization?: number
  market_share_percentage?: number
  
  // Financial Information
  balance_sheet_strength?: string
  funding_sources?: string[]
  recent_capital_raises?: string
  typical_debt_to_equity_ratio?: number
  development_fee_structure?: string
  credit_rating?: string
  dry_powder?: number
  annual_deployment_target?: number
  
  // Investment & Fund Information
  investment_vehicle_type?: string
  active_fund_name_series?: string
  fund_size_active_fund?: number
  fundraising_status?: string
  lender_type?: string
  annual_loan_volume?: number
  lending_origin_balance_sheet_securitization?: string
  portfolio_health?: string
  
  // Partnership & Leadership
  partnerships?: string[]
  key_equity_partners?: string[]
  key_debt_partners?: string[]
  board_of_directors?: string[]
  key_executives?: string[]
  founder_background?: string
  
  // Market Positioning & Strategy
  market_cycle_positioning?: string
  urban_vs_suburban_preference?: string
  sustainability_esg_focus?: boolean
  technology_proptech_adoption?: boolean
  adaptive_reuse_experience?: boolean
  regulatory_zoning_expertise?: boolean
  
  // Corporate Structure
  corporate_structure?: string
  parent_company?: string
  subsidiaries?: string[]
  stock_ticker_symbol?: string
  stock_exchange?: string
  
  // Business Information
  products_services_description?: string
  target_customer_profile?: string
  major_competitors?: string[]
  unique_selling_proposition?: string
  industry_awards_recognitions?: string[]
  company_history?: string
  
  // Transaction & Portfolio Data
  transactions_completed_last_12m?: number
  total_transaction_volume_ytd?: number
  deal_count_ytd?: number
  average_deal_size?: number
  portfolio_size_sqft?: number
  portfolio_asset_count?: number
  role_in_previous_deal?: string
  
  // Relationship & Pipeline Data
  internal_relationship_manager?: string
  last_contact_date?: string
  pipeline_status?: string
  recent_news_sentiment?: string
  
  // Data Quality & Processing
  data_source?: string
  data_confidence_score?: number
  quarterly_earnings_link?: string
}

export class CompanyOverviewProcessorV2 extends BaseProcessor {
  private llmProvider;
  private queryParams: any[] = []
  private queries: string[] = []
  private companiesProcessed: number = 0

  constructor(options: ProcessorOptions = {}) {
    // Perplexity API specific rate limiting configuration
    const perplexityBottleneckConfig = {
      maxConcurrent: 10,                   // Reduced from 5 to prevent queue overflow
      minTime: 1500,                      // 1.5 seconds between requests
      retryAttempts: 3,                   // Standard retries for LLM API
      retryDelayBase: 2000,               // 2 second base delay for retries
      retryDelayMax: 30000,               // Max 30 second retry delay
      timeout: 240000,                    // 4 minutes timeout for LLM processing
      highWater: 300,                     // Higher queue limit for batch processing
      strategy: 'OVERFLOW' as any,        // Use OVERFLOW strategy
      defaultPriority: 3,                 // Higher priority for company overview
      enableJobMetrics: true              // Track LLM API performance
    }

    super('CompanyOverviewV2', {
      ...options,
      bottleneckConfig: options.bottleneckConfig || perplexityBottleneckConfig
    })
    
    // Create a logger adapter that uses our log method
    const loggerAdapter = createProcessorLoggerAdapter(this.log.bind(this));
    
    // Use Perplexity for web-enhanced research capabilities
    this.llmProvider = LLMFactory.createProvider(
      'perplexity',
      loggerAdapter,
      {
        apiKey: process.env.PERPLEXITY_API_KEY,
      }
    );
  }

  async getUnprocessedEntities(): Promise<UnifiedEntityData[]> {
    const baseFilters = {

    }
    const specificFilters = {
      companyOverviewV2Status: 'pending'
    }
    return await this.getUnprocessedUnifiedEntities(baseFilters, specificFilters, 'company')
  }

  /**
   * Get default processor-specific filters when no UI filters are provided
   */
  protected getDefaultProcessorFilters(entityType?: 'contact' | 'company'): Record<string, any> {
    if (entityType === 'company') {
      return {
        companyOverviewV2Status: 'pending'
      }
    }
    return {}
  }

  async processEntity(entity: UnifiedEntityData): Promise<{ success: boolean; error?: string }> {
    // Only process companies for company overview
    if (entity.entity_type !== 'company') {
      return { success: false, error: 'Company overview only supports companies' }
    }
    
    try {
      this.log('info', `Extracting comprehensive company overview for: ${entity.company_name}`)

      // Set status to running
      await this.updateOverviewV2Status(entity.id, 'running')

      // Get company content
      const content = await this.getCompanyContent(entity.id)
      
      // if ((!content || content.trim().length === 0 ) && (!this.options.multiIds || this.options.multiIds.length === 0)) {
      //   this.log('warn', `No content found for company ${entity.id}`)
      //   await this.updateOverviewV2Status(entity.id, 'failed', 'No content available for extraction')
      //   return { success: false, error: 'No content available for extraction' }
      // }

      // Extract comprehensive company overview
      const { data: overviewData, llmResponse, llmUsage } = await this.extractCompanyOverviewV2(entity, content);
      
      if (!overviewData) {
        await this.updateOverviewV2Status(entity.id, 'failed', 'Failed to extract company overview')
        return { success: false, error: 'Failed to extract company overview' }
      }

      // Store the extracted data directly to companies table
      await this.storeCompanyOverviewV2(entity.id, overviewData, llmResponse || undefined, llmUsage || undefined)

      this.log('info', `Successfully extracted comprehensive company overview for ${entity.company_name}`)
      return { success: true }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      this.log('error', `Error extracting company overview for ${entity.company_name}: ${errorMessage}`)
      await this.updateOverviewV2Status(entity.id, 'failed', errorMessage)
      return { success: false, error: errorMessage }
    }
  }

  async updateEntityStatus(entityId: number, success: boolean): Promise<void> {
    if (success) {
      await this.updateOverviewV2Status(entityId, 'completed');
      this.log('info', `Company ${entityId} marked as processed with comprehensive data extracted`);
    } else {
      // For failed processing, mark as failed and increment error count
      await this.updateOverviewV2Status(entityId, 'failed',  "Failed to update comprehensive overview");
      await this.incrementProcessingErrorCount('company', entityId);
      this.log('info', `Company ${entityId} processing failed, marked as failed`);
    }
  }

  /**
   * Extract comprehensive company overview using LLM provider
   */
  private async extractCompanyOverviewV2(
    company: UnifiedEntityData, 
    content: string
  ): Promise<{ data: CompanyOverviewDataV2 | null; llmResponse?: string; llmUsage?: any }> {
    try {
      const messages = await this.buildExtractionMessagesV2(company, content);
      const model = 'sonar'

      // Use the LLM provider with fallback support and better model for web search
      const response = await this.llmProvider.callLLM(messages, {
        model: model,
        temperature: 0.3, // Lower temperature for more consistent extraction
        maxTokens: 15000 // Increased token limit for comprehensive extraction
      });

      if (!response.content) {
        throw new Error(`No response content from ${response.provider}`);
      }

      this.log('info', `Extracted comprehensive overview using ${response.provider} (${response.model})`);

      // Parse the structured response
      const parsedResponse = this.parseOverviewResponseV2(response.content);
      
      return {
        data: parsedResponse,
        llmResponse: response.content,
        llmUsage: response.usage
      };
    } catch (error) {
      this.log('error', `Error calling LLM extraction API: ${error}`);
      return { data: null };
    }
  }

  /**
   * Build messages for LLM comprehensive company overview extraction
   */
  private async buildExtractionMessagesV2(
    company: UnifiedEntityData, 
    content: string
  ): Promise<LLMMessage[]> {
    // Use the static getMappings method from BaseScraper
    const mappings = await BaseScraper.getMappings();
          const userPrompt = COMPANY_OVERVIEW_V2_USER_TEMPLATE_FUNCTION({
      company_name: company.company_name || '',
      company_website: company.company_website || '',
      industry: company.industry || ''
    }, content, mappings);

    return [
      { role: 'system', content: COMPANY_OVERVIEW_V2_SYSTEM_PROMPT },
      { role: 'user', content: userPrompt }
    ]
  }

  /**
   * Parse LLM response to extract comprehensive company overview data
   */
  private parseOverviewResponseV2(content: string): CompanyOverviewDataV2 | null {
    try {
      this.log('debug', `Parsing comprehensive LLM response content (length: ${content.length})`);
      
      // Clean the content and extract JSON
      let jsonContent = content.trim();
      
      // Handle Perplexity format: JSON comes AFTER </think> tag, not inside it
      const thinkMatch = jsonContent.match(/<think>[\s\S]*?<\/think>\s*([\s\S]*)/);
      if (thinkMatch) {
        // Extract content that comes AFTER the </think> tag
        const afterThinkContent = thinkMatch[1].trim();
        this.log('debug', `Extracted content after <think> tags (${afterThinkContent.length} chars)`);
        
        if (afterThinkContent) {
          jsonContent = afterThinkContent;
          this.log('debug', `Using content after </think> tag`);
        } else {
          throw new Error('No content found after </think> tag');
        }
      } else {
        // Remove any thinking tags if they appear without content
        jsonContent = jsonContent.replace(/<think>[\s\S]*?<\/think>/g, '');
        
        // Remove any leading/trailing text that isn't JSON
        jsonContent = jsonContent.replace(/^[^{]*/, '').replace(/[^}]*$/, '');
        
        // Try to extract from markdown code blocks
        const markdownJsonMatch = jsonContent.match(/```json\s*([\s\S]*?)\s*```/);
        if (markdownJsonMatch) {
          jsonContent = markdownJsonMatch[1].trim();
          this.log('debug', `Extracted JSON from markdown code block`);
        } else {
          // Look for the main JSON object
          const jsonMatch = jsonContent.match(/\{[\s\S]*\}/);
          if (!jsonMatch) {
            throw new Error('No JSON found in response');
          }
          jsonContent = jsonMatch[0];
          this.log('debug', `Extracted JSON using direct object match`);
        }
      }

      // Clean up the JSON content
      jsonContent = jsonContent.trim()

      // Check if JSON appears to be truncated (doesn't end with closing brace)
      if (!jsonContent.endsWith('}')) {
        this.log('warn', `JSON appears to be truncated (doesn't end with }). Attempting to parse anyway.`)
      }

      const result = JSON.parse(jsonContent)

      this.log('info', `Successfully parsed comprehensive extraction response with ${Object.keys(result).length} fields`)
      
      // Handle nested structure from prompt: { "company": { ... } }
      let companyData = result;
      if (result.company && typeof result.company === 'object') {
        companyData = result.company;
        this.log('debug', `Extracted company data from nested structure`);
      }
      
      // Clean and return the result
      return this.cleanNullValues(companyData) as CompanyOverviewDataV2
    } catch (error) {
      this.log('error', `Error parsing comprehensive overview response: ${error}`);
      
      // Log a portion of the content for debugging
      const contentPreview = content.length > 1500 ? content.substring(0, 1500) + '...' : content;
      this.log('debug', `Response content preview: ${contentPreview}`);
      
      return null;
    }
  }

  /**
   * Store comprehensive extracted company overview directly in companies table
   */
  private async storeCompanyOverviewV2(companyId: number, data: CompanyOverviewDataV2, llmResponse?: string, llmUsage?: any): Promise<void> {
    this.log('debug', `Storing comprehensive overview data for company ${companyId}`);
    this.log('debug', `LLM Usage type: ${typeof llmUsage}, value: ${JSON.stringify(llmUsage)}`);
    
    const sql = `
      UPDATE companies SET
        -- Core Company Information
        industry = COALESCE($1, industry),
        company_type = COALESCE($2, company_type),
        business_model = COALESCE($3, business_model),
        founded_year = COALESCE($4, founded_year),
        
        -- Investment & Strategy
        investment_focus = COALESCE($5, investment_focus),
        investment_strategy_mission = COALESCE($6, investment_strategy_mission),
        investment_strategy_approach = COALESCE($7, investment_strategy_approach),
        
        -- Contact Information (AI-updated fields)
        main_phone = COALESCE($8, main_phone),
        secondary_phone = COALESCE($9, secondary_phone),
        main_email = COALESCE($10, main_email),
        secondary_email = COALESCE($11, secondary_email),
        company_linkedin = COALESCE($12, company_linkedin),
        twitter = COALESCE($13, twitter),
        facebook = COALESCE($14, facebook),
        instagram = COALESCE($15, instagram),
        youtube = COALESCE($16, youtube),
        
        -- Address Information (AI-updated headquarters fields)
        headquarters_address = COALESCE($17, headquarters_address),
        headquarters_city = COALESCE($18, headquarters_city),
        headquarters_state = COALESCE($19, headquarters_state),
        headquarters_zipcode = COALESCE($20, headquarters_zipcode),
        headquarters_country = COALESCE($21, headquarters_country),
        additional_address = COALESCE($22, additional_address),
        additional_city = COALESCE($23, additional_city),
        additional_state = COALESCE($24, additional_state),
        additional_zipcode = COALESCE($25, additional_zipcode),
        additional_country = COALESCE($26, additional_country),
        office_locations = COALESCE($27, office_locations),
        
        -- Company Metrics
        fund_size = COALESCE($28, fund_size),
        aum = COALESCE($29, aum),
        number_of_properties = COALESCE($30, number_of_properties),
        number_of_offices = COALESCE($31, number_of_offices),
        number_of_employees = COALESCE($32, number_of_employees),
        annual_revenue = COALESCE($33, annual_revenue),
        net_income = COALESCE($34, net_income),
        ebitda = COALESCE($35, ebitda),
        profit_margin = COALESCE($36, profit_margin),
        market_capitalization = COALESCE($37, market_capitalization),
        market_share_percentage = COALESCE($38, market_share_percentage),
        
        -- Financial Information
        balance_sheet_strength = COALESCE($39, balance_sheet_strength),
        funding_sources = COALESCE($40, funding_sources),
        recent_capital_raises = COALESCE($41, recent_capital_raises),
        typical_debt_to_equity_ratio = COALESCE($42, typical_debt_to_equity_ratio),
        development_fee_structure = COALESCE($43, development_fee_structure),
        credit_rating = COALESCE($44, credit_rating),
        dry_powder = COALESCE($45, dry_powder),
        annual_deployment_target = COALESCE($46, annual_deployment_target),
        
        -- Investment & Fund Information
        investment_vehicle_type = COALESCE($47, investment_vehicle_type),
        active_fund_name_series = COALESCE($48, active_fund_name_series),
        fund_size_active_fund = COALESCE($49, fund_size_active_fund),
        fundraising_status = COALESCE($50, fundraising_status),
        lender_type = COALESCE($51, lender_type),
        annual_loan_volume = COALESCE($52, annual_loan_volume),
        lending_origin = COALESCE($53, lending_origin),
        portfolio_health = COALESCE($54, portfolio_health),
        
        -- Partnership & Leadership
        partnerships = COALESCE($55, partnerships),
        key_equity_partners = COALESCE($56, key_equity_partners),
        key_debt_partners = COALESCE($57, key_debt_partners),
        board_of_directors = COALESCE($58, board_of_directors),
        key_executives = COALESCE($59, key_executives),
        founder_background = COALESCE($60, founder_background),
        
        -- Market Positioning & Strategy
        market_cycle_positioning = COALESCE($61, market_cycle_positioning),
        urban_vs_suburban_preference = COALESCE($62, urban_vs_suburban_preference),
        sustainability_esg_focus = COALESCE($63, sustainability_esg_focus),
        technology_proptech_adoption = COALESCE($64, technology_proptech_adoption),
        adaptive_reuse_experience = COALESCE($65, adaptive_reuse_experience),
        regulatory_zoning_expertise = COALESCE($66, regulatory_zoning_expertise),
        
        -- Corporate Structure
        corporate_structure = COALESCE($67, corporate_structure),
        parent_company = COALESCE($68, parent_company),
        subsidiaries = COALESCE($69, subsidiaries),
        stock_ticker_symbol = COALESCE($70, stock_ticker_symbol),
        stock_exchange = COALESCE($71, stock_exchange),
        
        -- Business Information
        products_services_description = COALESCE($72, products_services_description),
        target_customer_profile = COALESCE($73, target_customer_profile),
        major_competitors = COALESCE($74, major_competitors),
        unique_selling_proposition = COALESCE($75, unique_selling_proposition),
        industry_awards_recognitions = COALESCE($76, industry_awards_recognitions),
        company_history = COALESCE($77, company_history),
        
        -- Transaction & Portfolio Data
        transactions_completed_last_12m = COALESCE($78, transactions_completed_last_12m),
        total_transaction_volume_ytd = COALESCE($79, total_transaction_volume_ytd),
        deal_count_ytd = COALESCE($80, deal_count_ytd),
        average_deal_size = COALESCE($81, average_deal_size),
        portfolio_size_sqft = COALESCE($82, portfolio_size_sqft),
        portfolio_asset_count = COALESCE($83, portfolio_asset_count),
        role_in_previous_deal = COALESCE($84, role_in_previous_deal),
        
        -- Relationship & Pipeline Data
        internal_relationship_manager = COALESCE($85, internal_relationship_manager),
        last_contact_date = COALESCE($86, last_contact_date),
        pipeline_status = COALESCE($87, pipeline_status),
        recent_news_sentiment = COALESCE($88, recent_news_sentiment),
        
        -- Data Quality & Processing
        data_source = COALESCE($89, data_source),
        data_confidence_score = COALESCE($90, data_confidence_score),
        quarterly_earnings_link = COALESCE($91, quarterly_earnings_link),
        
        -- Processing metadata
        updated_at = NOW(),
        last_updated_timestamp = NOW(),
        llm_used = $92,
        llm_response = $93,
        llm_token_usage = $94
        
      WHERE company_id = $95
    `
    
    // Prepare parameters array
    const params = [
      data.company_industry || null,                          // $1 - industry field
      data.company_type || null,                              // $2
      data.business_model || null,                            // $3
      data.founded_year || null,                              // $4
      data.investment_focus ? this.arrayToPostgresArray(data.investment_focus) : null,  // $5
      data.investment_strategy_mission || null,               // $6
      data.investment_strategy_approach || null,              // $7
      data.main_phone || null,                                // $8
      data.secondary_phone || null,                           // $9
      data.main_email || null,                                // $10
      data.secondary_email || null,                           // $11
      data.linkedin || null,                                  // $12
      data.twitter || null,                                   // $13
      data.facebook || null,                                  // $14
      data.instagram || null,                                 // $15
      data.youtube || null,                                   // $16
      data.headquarters_address || null,                      // $17
      data.headquarters_city || null,                         // $18
      data.headquarters_state || null,                        // $19
      data.headquarters_zipcode || null,                      // $20
      data.headquarters_country || null,                      // $21
      data.additional_address || null,                        // $22
      data.additional_city || null,                           // $23
      data.additional_state || null,                          // $24
      data.additional_zipcode || null,                        // $25
      data.additional_country || null,                        // $26
      data.office_locations ? this.arrayToPostgresArray(data.office_locations) : null,  // $27
      data.fund_size || null,                                 // $28
      data.aum || null,                                       // $29
      data.number_of_properties || null,                      // $30
      data.number_of_offices || null,                         // $31
      data.number_of_employees || null,                       // $32
      data.annual_revenue || null,                            // $33
      data.net_income || null,                                // $34
      data.ebitda || null,                                    // $35
      data.profit_margin || null,                             // $36
      data.market_capitalization || null,                     // $37
      data.market_share_percentage || null,                   // $38
      data.balance_sheet_strength || null,                    // $39
      data.funding_sources ? this.arrayToPostgresArray(data.funding_sources) : null,    // $40
      data.recent_capital_raises || null,                     // $41
      data.typical_debt_to_equity_ratio || null,              // $42
      data.development_fee_structure || null,                 // $43
      data.credit_rating || null,                             // $44
      data.dry_powder || null,                                // $45
      data.annual_deployment_target || null,                  // $46
      data.investment_vehicle_type || null,                   // $47
      data.active_fund_name_series || null,                   // $48
      data.fund_size_active_fund || null,                     // $49
      data.fundraising_status || null,                        // $50
      data.lender_type || null,                               // $51
      data.annual_loan_volume || null,                        // $52
      data.lending_origin_balance_sheet_securitization || null, // $53 - map to lending_origin column
      data.portfolio_health || null,                          // $54
      data.partnerships ? this.arrayToPostgresArray(data.partnerships) : null,        // $55
      data.key_equity_partners ? this.arrayToPostgresArray(data.key_equity_partners) : null,  // $56
      data.key_debt_partners ? this.arrayToPostgresArray(data.key_debt_partners) : null,  // $57
      data.board_of_directors ? this.arrayToPostgresArray(data.board_of_directors) : null,  // $58
      data.key_executives ? this.arrayToPostgresArray(data.key_executives) : null,      // $59
      data.founder_background || null,                        // $60
      data.market_cycle_positioning || null,                  // $61
      data.urban_vs_suburban_preference || null,              // $62
      data.sustainability_esg_focus || null,                  // $63
      data.technology_proptech_adoption || null,              // $64
      data.adaptive_reuse_experience || null,                 // $65
      data.regulatory_zoning_expertise || null,               // $66
      data.corporate_structure || null,                       // $67
      data.parent_company || null,                            // $68
      data.subsidiaries ? this.arrayToPostgresArray(data.subsidiaries) : null,        // $69
      data.stock_ticker_symbol || null,                       // $70
      data.stock_exchange || null,                            // $71
      data.products_services_description || null,             // $72
      data.target_customer_profile || null,                   // $73
      data.major_competitors ? this.arrayToPostgresArray(data.major_competitors) : null,  // $74
      data.unique_selling_proposition || null,                // $75
      data.industry_awards_recognitions ? this.arrayToPostgresArray(data.industry_awards_recognitions) : null,  // $76
      data.company_history || null,                           // $77
      data.transactions_completed_last_12m || null,           // $78
      data.total_transaction_volume_ytd || null,              // $79
      data.deal_count_ytd || null,                            // $80
      data.average_deal_size || null,                         // $81
      data.portfolio_size_sqft || null,                       // $82
      data.portfolio_asset_count || null,                     // $83
      data.role_in_previous_deal || null,                     // $84
      data.internal_relationship_manager || null,             // $85
      data.last_contact_date || null,                         // $86
      data.pipeline_status || null,                           // $87
      data.recent_news_sentiment || null,                     // $88
      data.data_source || null,                               // $89
      data.data_confidence_score || null,                     // $90
        data.quarterly_earnings_link || null,                   // $91
      'perplexity-sonar',                                     // $92 - LLM used
      llmResponse || null,                                    // $93 - LLM response
      llmUsage && typeof llmUsage === 'object' ? JSON.stringify(llmUsage) : null,  // $94 - LLM usage
      companyId                                               // $95
    ]
    
    await this.query(sql, params)
    
    this.log('debug', `Stored comprehensive company overview for company ${companyId}`)
  }

  /**
   * Convert JavaScript array to PostgreSQL array literal format
   */
  private arrayToPostgresArray(arr: string[] | null | undefined): string {
    if (!arr || arr.length === 0) {
      return '{}' // Return empty PostgreSQL array instead of null
    }
    
    // Escape quotes and wrap values that contain spaces or special characters
    const escapedValues = arr.map(value => {
      if (typeof value !== 'string') {
        return String(value)
      }
      
      // If value contains spaces, commas, quotes, or special characters, wrap in quotes and escape
      if (value.includes(' ') || value.includes(',') || value.includes('"') || value.includes('\'') || value.includes('{') || value.includes('}')) {
        return `"${value.replace(/"/g, '\\"')}"` 
      }
      
      return value
    })
    
    return `{${escapedValues.join(',')}}`
  }

  /**
   * Clean out null, undefined, and empty values from an object
   */
  private cleanNullValues<T>(obj: T): T {
    const result = { ...obj } as any
    
    for (const key in result) {
      const value = result[key]
      
      // Remove null/undefined values
      if (value === null || value === undefined) {
        delete result[key]
        continue
      }
      
      // Handle arrays - remove empty arrays and clean arrays of objects
      if (Array.isArray(value)) {
        if (value.length === 0) {
          delete result[key]
        } else if (typeof value[0] === 'object') {
          result[key] = value.map(item => this.cleanNullValues(item))
            .filter(item => Object.keys(item).length > 0)
          
          if (result[key].length === 0) {
            delete result[key]
          }
        }
      }
      
      // Handle objects recursively
      else if (typeof value === 'object') {
        result[key] = this.cleanNullValues(value)
        if (Object.keys(result[key]).length === 0) {
          delete result[key]
        }
      }
      
      // Handle empty strings
      else if (typeof value === 'string' && value.trim() === '') {
        delete result[key]
      }
    }
    
    return result as T
  }
}
