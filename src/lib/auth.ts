import { NextAuthOptions, Session, User } from "next-auth"
import Cred<PERSON><PERSON><PERSON><PERSON><PERSON> from "next-auth/providers/credentials"
import { JWT } from "next-auth/jwt"
import bcrypt from "bcryptjs"
import { pool } from "./db"

// Extended user type for NextAuth
interface ExtendedUser extends User {
  user_id: number;
  role: string;
  is_admin: boolean;
  permissions: string[];
  impersonated_by?: number;
  original_user_id?: number;
  force_password_change?: boolean;
}

// Extended session type
interface ExtendedSession extends Session {
  user: ExtendedUser;
  isImpersonating?: boolean;
  originalAdmin?: ExtendedUser;
}

// Extended JWT type
interface ExtendedJWT extends JWT {
  user_id?: number;
  is_admin?: boolean;
  permissions?: string[];
  impersonated_by?: number;
  original_user_id?: number;
  impersonation_token?: string;
}

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
        impersonation_token: { label: "Impersonation Token", type: "hidden" }
      },
      async authorize(credentials) {
        if (!credentials?.email) {
          return null
        }

        try {
          // Handle impersonation token authentication
          if (credentials.impersonation_token && !credentials.password) {
            // Verify and decode impersonation token
            const { jwtVerify } = await import("jose");
            const secret = new TextEncoder().encode(
              process.env.NEXTAUTH_SECRET || 'fallback-secret-key'
            );

            try {
              const { payload } = await jwtVerify(credentials.impersonation_token, secret);
              
              // Get user being impersonated
              const userResult = await pool.query(`
                SELECT 
                  user_id, first_name, last_name, email, role, 
                  is_admin, is_active, permissions, impersonated_by
                FROM users 
                WHERE user_id = $1 AND deleted_at IS NULL AND is_active = true
              `, [payload.sub]);

              if (userResult.rows.length === 0) {
                return null;
              }

              const user = userResult.rows[0];
              
              return {
                id: user.user_id.toString(),
                user_id: user.user_id,
                name: `${user.first_name} ${user.last_name}`,
                email: user.email,
                username: user.email, // Use email as username for compatibility
                role: user.role,
                is_admin: user.is_admin,
                permissions: user.permissions || [],
                impersonated_by: payload.impersonatedBy,
                original_user_id: payload.originalUserId
              };
            } catch (tokenError) {
              console.error("Invalid impersonation token:", tokenError);
              return null;
            }
          }

          // Regular password authentication
          if (!credentials.password) {
            return null
          }

          // Get user from database - handle both current and migrated table structure
          const userResult = await pool.query(`
            SELECT 
              user_id, 
              COALESCE(first_name, SPLIT_PART(display_name, ' ', 1)) as first_name,
              COALESCE(last_name, CASE WHEN display_name IS NOT NULL THEN SPLIT_PART(display_name, ' ', 2) ELSE username END) as last_name,
              email, username, password_hash, display_name,
              salt, role, 
              COALESCE(is_admin, (role = 'admin')::boolean) as is_admin, 
              COALESCE(is_active, true) as is_active, 
              COALESCE(permissions, ARRAY[]::TEXT[]) as permissions, 
              COALESCE(failed_login_attempts, 0) as failed_login_attempts, 
              locked_until, force_password_change, impersonated_by
            FROM users 
            WHERE email = $1
          `, [credentials.email]);

          if (userResult.rows.length === 0) {
            return null;
          }

          const user = userResult.rows[0];

          // Check if user is active
          if (!user.is_active) {
            return null;
          }

          // Check if account is locked
          if (user.locked_until && new Date() < new Date(user.locked_until)) {
            return null;
          }

          // Verify password - handle both bcrypt and potentially plain text (current table)
          let isPasswordValid = false;
          
          try {
            // Try bcrypt first (preferred method)
            isPasswordValid = await bcrypt.compare(credentials.password, user.password_hash);
          } catch (bcryptError) {
            // If bcrypt fails, might be plain text (only for existing users before migration)
            console.warn('bcrypt verification failed, checking for plain text password (deprecated)');
            isPasswordValid = credentials.password === user.password_hash;
          }
          
          if (!isPasswordValid) {
            // Try to increment failed login attempts (column might not exist)
            try {
              await pool.query(`
                UPDATE users 
                SET failed_login_attempts = COALESCE(failed_login_attempts, 0) + 1,
                    locked_until = CASE 
                      WHEN COALESCE(failed_login_attempts, 0) + 1 >= 5 
                      THEN CURRENT_TIMESTAMP + INTERVAL '30 minutes'
                      ELSE locked_until
                    END
                WHERE user_id = $1
              `, [user.user_id]);
            } catch (updateError) {
              console.warn('Could not update failed login attempts (column may not exist)');
            }
            
            return null;
          }

          // Reset failed attempts and update last login (handle missing columns)
          try {
            await pool.query(`
              UPDATE users 
              SET 
                failed_login_attempts = 0,
                locked_until = NULL,
                last_login = CURRENT_TIMESTAMP,
                login_count = COALESCE(login_count, 0) + 1
              WHERE user_id = $1
            `, [user.user_id]);
          } catch (updateError) {
            console.warn('Could not update login tracking (columns may not exist yet)');
            // Just update what we can
            await pool.query(`UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE user_id = $1`, [user.user_id]);
          }

          // Return authenticated user
          return {
            id: user.user_id.toString(),
            user_id: user.user_id,
            name: `${user.first_name} ${user.last_name}`,
            email: user.email,
            username: user.username || user.email, // Use username or email as fallback
            role: user.role,
            is_admin: user.is_admin,
            permissions: user.permissions || [],
            force_password_change: user.force_password_change,
            impersonated_by: user.impersonated_by
          };

        } catch (error) {
          console.error("Authentication error:", error);
          return null;
        }
      }
    })
  ],
  session: {
    strategy: "jwt",
    maxAge: 24 * 60 * 60 // 24 hours
  },
  pages: {
    signIn: "/login",
    error: "/login"
  },
  callbacks: {
    async jwt({ token, user }): Promise<ExtendedJWT> {
      // Initial sign in
      if (user) {
        const extUser = user as ExtendedUser;
        token.user_id = extUser.user_id;
        token.role = extUser.role;
        token.is_admin = extUser.is_admin;
        token.permissions = extUser.permissions;
        token.impersonated_by = extUser.impersonated_by;
        token.original_user_id = extUser.original_user_id;
      } else if (token.email) {
        // Token refresh - get fresh user data to ensure admin status is current
        try {
          const userResult = await pool.query(`
            SELECT 
              user_id, role, 
              COALESCE(is_admin, (role = 'admin')::boolean) as is_admin, 
              COALESCE(is_active, true) as is_active, 
              COALESCE(permissions, ARRAY[]::TEXT[]) as permissions,
              impersonated_by, original_user_id
            FROM users 
            WHERE email = $1 AND deleted_at IS NULL
          `, [token.email]);

          if (userResult.rows.length > 0) {
            const user = userResult.rows[0];
            token.user_id = user.user_id;
            token.role = user.role;
            token.is_admin = user.is_admin;
            token.permissions = user.permissions || [];
            token.impersonated_by = user.impersonated_by;
            token.original_user_id = user.original_user_id;
          }
        } catch (error) {
          console.error("Error refreshing user data in JWT callback:", error);
          // Don't fail the token refresh, just keep existing data
        }
      }
      return token as ExtendedJWT;
    },
    
    async session({ session, token }): Promise<ExtendedSession> {
      const extToken = token as ExtendedJWT;
      
      // Add custom fields to session
      if (extToken && session.user) {
        const extUser: ExtendedUser = {
          ...session.user,
          user_id: extToken.user_id!,
          role: extToken.role!,
          is_admin: extToken.is_admin!,
          permissions: extToken.permissions || [],
          impersonated_by: extToken.impersonated_by,
          original_user_id: extToken.original_user_id
        };

        const extSession: ExtendedSession = {
          ...session,
          user: extUser,
          isImpersonating: !!extToken.impersonated_by
        };

        // If impersonating, get original admin info
        if (extToken.impersonated_by) {
          try {
            const adminResult = await pool.query(`
              SELECT user_id, first_name, last_name, email, role, is_admin 
              FROM users 
              WHERE user_id = $1 AND deleted_at IS NULL
            `, [extToken.impersonated_by]);

            if (adminResult.rows.length > 0) {
              const admin = adminResult.rows[0];
              extSession.originalAdmin = {
                id: admin.user_id.toString(),
                user_id: admin.user_id,
                name: `${admin.first_name} ${admin.last_name}`,
                email: admin.email,
                username: admin.email, // Use email as username for compatibility
                role: admin.role,
                is_admin: admin.is_admin,
                permissions: []
              };
            }
          } catch (error) {
            console.error("Error fetching original admin:", error);
          }
        }

        return extSession;
      }
      
      return session as ExtendedSession;
    }
  },
  events: {
    async signIn({ user, account, profile, isNewUser }) {
      // Log sign in activity
      if (user && 'user_id' in user) {
        const extUser = user as ExtendedUser;
        try {
          await pool.query(`
            INSERT INTO user_activity_log (user_id, action, description)
            VALUES ($1, $2, $3)
          `, [
            extUser.user_id,
            'user_signin',
            `User signed in${extUser.impersonated_by ? ' (impersonation)' : ''}`
          ]);
        } catch (error) {
          console.error("Error logging sign in:", error);
        }
      }
    },
    
    async signOut({ session, token }) {
      // Log sign out activity and clean up impersonation
      if (token && 'user_id' in token) {
        try {
          await pool.query(`
            INSERT INTO user_activity_log (user_id, action, description)
            VALUES ($1, $2, $3)
          `, [
            token.user_id,
            'user_signout',
            'User signed out'
          ]);

          // Clean up impersonation if active
          if (token.impersonated_by) {
            await pool.query(`
              UPDATE users 
              SET impersonated_by = NULL, impersonation_started_at = NULL
              WHERE user_id = $1
            `, [token.user_id]);
          }
        } catch (error) {
          console.error("Error logging sign out:", error);
        }
      }
    }
  }
} 