import { pool } from '@/lib/db'
import { normalizeCompanyName, extractDomain, tokenizeName, normalizePhone, normalizeEmail, extractLinkedInHandle } from '@/lib/utils/normalizationUtils'


export interface CompanyNormalizationData {
  company_id: number
  normalized_name: string
  normalized_domain: string | null
  name_tokens: string[]
  industry_normalized: string | null
  phone_normalized: string | null
}

export interface ContactNormalizationData {
  contact_id: number
  full_name_normalized: string
  email_normalized: string | null
  linkedin_handle: string | null
  phone_normalized: string | null
  name_tokens: string[]
  company_name_normalized: string | null
  title_normalized: string | null
  additional_email_normalized: string | null
  email_domain: string | null
  first_name_normalized: string | null
  last_name_normalized: string | null
}

export class DataNormalizationService {
  
  /**
   * Populate normalized data for a single company
   */
  static async normalizeCompany(companyId: number): Promise<void> {
    const client = await pool.connect()
    
    try {
      // Get company data
      const companyResult = await client.query(`
        SELECT company_id, company_name, company_website, industry, company_phone
        FROM companies 
        WHERE company_id = $1
      `, [companyId])
      
      if (companyResult.rows.length === 0) {
        throw new Error(`Company with ID ${companyId} not found`)
      }
      
      const company = companyResult.rows[0]
      
      const normalizedData: CompanyNormalizationData = {
        company_id: company.company_id,
        normalized_name: normalizeCompanyName(company.company_name),
        name_tokens: tokenizeName(company.company_name),
        normalized_domain: extractDomain(company.company_website),
        industry_normalized: company.industry ? company.industry.toLowerCase().trim() : null,
        phone_normalized: normalizePhone(company.company_phone)
      }
      
      // Check if record exists and update or insert accordingly
      const existingResult = await client.query(`
        SELECT id FROM company_normalized_data WHERE company_id = $1
      `, [normalizedData.company_id])

      if (existingResult.rows.length > 0) {
        // Update existing record
        await client.query(`
          UPDATE company_normalized_data SET
            normalized_name = $2,
            normalized_domain = $3,
            name_tokens = $4,
            industry_normalized = $5,
            phone_normalized = $6,
            updated_at = CURRENT_TIMESTAMP
          WHERE company_id = $1
        `, [
          normalizedData.company_id,
          normalizedData.normalized_name,
          normalizedData.normalized_domain,
          normalizedData.name_tokens,
          normalizedData.industry_normalized,
          normalizedData.phone_normalized
        ])
      } else {
        // Insert new record
        await client.query(`
          INSERT INTO company_normalized_data (
            company_id, normalized_name, normalized_domain, name_tokens,
            industry_normalized, phone_normalized, created_at, updated_at
          ) VALUES ($1, $2, $3, $4, $5, $6, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        `, [
          normalizedData.company_id,
          normalizedData.normalized_name,
          normalizedData.normalized_domain,
          normalizedData.name_tokens,
          normalizedData.industry_normalized,
          normalizedData.phone_normalized
        ])
      }
      
    } finally {
      client.release()
    }
  }

  /**
   * Populate normalized data for a single contact
   */
  static async normalizeContact(contactId: number): Promise<void> {
    const client = await pool.connect()
    
    try {
      // Get contact data
      const contactResult = await client.query(`
        SELECT contact_id, full_name, email, linkedin_url, phone_number, c.company_name, title, additional_email, first_name, last_name
        FROM contacts
        join companies c on contacts.company_id = c.company_id
        WHERE contact_id = $1
      `, [contactId])
      
      if (contactResult.rows.length === 0) {
        throw new Error(`Contact with ID ${contactId} not found`)
      }
      
      const contact = contactResult.rows[0]
      
      const normalizedData: ContactNormalizationData = {
        contact_id: contact.contact_id,
        full_name_normalized: contact.full_name ? contact.full_name.toLowerCase().trim() : '',
        email_normalized: normalizeEmail(contact.email),
        linkedin_handle: extractLinkedInHandle(contact.linkedin_url),
        phone_normalized: normalizePhone(contact.phone_number),
        name_tokens: tokenizeName(contact.full_name),
        company_name_normalized: contact.company_name ? contact.company_name.toLowerCase().trim() : '',
        title_normalized: contact.title ? contact.title.toLowerCase().trim() : '',
        additional_email_normalized: contact.additional_email ? contact.additional_email.toLowerCase().trim() : '',
        email_domain: contact.email ? extractDomain(contact.email) : '',
        first_name_normalized: contact.first_name ? contact.first_name.toLowerCase().trim() : '',
        last_name_normalized: contact.last_name ? contact.last_name.toLowerCase().trim() : ''
      }
      
      // Check if record exists and update or insert accordingly
      const existingResult = await client.query(`
        SELECT id FROM contact_normalized_data WHERE contact_id = $1
      `, [normalizedData.contact_id])

      if (existingResult.rows.length > 0) {
        // Update existing record
        await client.query(`
          UPDATE contact_normalized_data SET
            full_name_normalized = $2,
            email_normalized = $3,
            linkedin_handle = $4,
            phone_normalized = $5,
            name_tokens = $6,
            company_name_normalized = $7,
            title_normalized = $8,
            additional_email_normalized = $9,
            email_domain = $10,
            first_name_normalized = $11,
            last_name_normalized = $12,
            updated_at = CURRENT_TIMESTAMP
          WHERE contact_id = $1
        `, [
          normalizedData.contact_id,
          normalizedData.full_name_normalized,
          normalizedData.email_normalized,
          normalizedData.linkedin_handle,
          normalizedData.phone_normalized,
          normalizedData.name_tokens,
          normalizedData.company_name_normalized,
          normalizedData.title_normalized,
          normalizedData.additional_email_normalized,
          normalizedData.email_domain || null,
          normalizedData.first_name_normalized,
          normalizedData.last_name_normalized
        ])
      } else {
        // Insert new record
        await client.query(`
          INSERT INTO contact_normalized_data (
            contact_id, full_name_normalized, email_normalized, linkedin_handle,
            phone_normalized, name_tokens, company_name_normalized, title_normalized, additional_email_normalized, email_domain, first_name_normalized, last_name_normalized, created_at, updated_at
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        `, [
          normalizedData.contact_id,
          normalizedData.full_name_normalized,
          normalizedData.email_normalized,
          normalizedData.linkedin_handle,
          normalizedData.phone_normalized,
          normalizedData.name_tokens,
          normalizedData.company_name_normalized,
          normalizedData.title_normalized,
          normalizedData.additional_email_normalized,
          normalizedData.email_domain || null,
          normalizedData.first_name_normalized,
          normalizedData.last_name_normalized
        ])
      }
      
    } finally {
      client.release()
    }
  }

  /**
   * Batch normalize companies using bulk operations - MUCH FASTER
   */
  static async normalizeAllCompaniesFast(batchSize: number = 1000): Promise<{ processed: number, errors: number }> {
    const client = await pool.connect()
    let processed = 0
    let errors = 0

    try {
      // Get total count
      const countResult = await client.query('SELECT COUNT(*) as total FROM companies')
      const total = parseInt(countResult.rows[0].total)

      console.log(`Starting FAST normalization of ${total} companies in batches of ${batchSize}`)

      for (let offset = 0; offset < total; offset += batchSize) {
        try {
          // Process entire batch in one query - UPSERT approach
          const result = await client.query(`
            INSERT INTO company_normalized_data (
              company_id, normalized_name, normalized_domain, name_tokens,
              industry_normalized, phone_normalized, website_normalized, address_normalized,
              created_at, updated_at
            )
            SELECT
              c.company_id,
              COALESCE(normalize_company_name(c.company_name), 'unknown company') as normalized_name,
              extract_domain(c.company_website) as normalized_domain,
              create_name_tokens(c.company_name) as name_tokens,
              LOWER(COALESCE(c.industry, '')) as industry_normalized,
              normalize_phone(c.company_phone) as phone_normalized,
              c.company_website as website_normalized,
              COALESCE(c.company_address, '') as address_normalized,
              CURRENT_TIMESTAMP as created_at,
              CURRENT_TIMESTAMP as updated_at
            FROM companies c
            ORDER BY c.company_id
            LIMIT $1 OFFSET $2
            ON CONFLICT (company_id) DO UPDATE SET
              normalized_name = EXCLUDED.normalized_name,
              normalized_domain = EXCLUDED.normalized_domain,
              name_tokens = EXCLUDED.name_tokens,
              industry_normalized = EXCLUDED.industry_normalized,
              phone_normalized = EXCLUDED.phone_normalized,
              website_normalized = EXCLUDED.website_normalized,
              address_normalized = EXCLUDED.address_normalized,
              updated_at = CURRENT_TIMESTAMP
          `, [batchSize, offset])

          processed += result.rowCount || 0
          console.log(`Processed batch ${Math.floor(offset/batchSize) + 1}: ${result.rowCount} records processed, total: ${processed}/${total}`)

        } catch (error) {
          console.error(`Error processing batch at offset ${offset}:`, error)
          errors++
        }
      }

    } finally {
      client.release()
    }

    return { processed, errors }
  }

  /**
   * Batch normalize all companies (legacy method - slower)
   */
  static async normalizeAllCompanies(batchSize: number = 100): Promise<{ processed: number, errors: number }> {
    // Use the fast method instead
    return this.normalizeAllCompaniesFast(batchSize * 10)
  }

  /**
   * Batch normalize contacts using bulk operations - MUCH FASTER
   */
  static async normalizeAllContactsFast(batchSize: number = 1000): Promise<{ processed: number, errors: number }> {
    const client = await pool.connect()
    let processed = 0
    let errors = 0

    try {
      // Get total count
      const countResult = await client.query('SELECT COUNT(*) as total FROM contacts')
      const total = parseInt(countResult.rows[0].total)

      console.log(`Starting FAST normalization of ${total} contacts in batches of ${batchSize}`)

      for (let offset = 0; offset < total; offset += batchSize) {
        try {
          // Process entire batch in one query - UPSERT approach
          const result = await client.query(`
            INSERT INTO contact_normalized_data (
              contact_id, full_name_normalized, email_normalized, linkedin_handle,
              phone_normalized, name_tokens, first_name_normalized, last_name_normalized,
              additional_email_normalized, email_domain, company_name_normalized, title_normalized,
              created_at, updated_at
            )
            SELECT
              c.contact_id,
              LOWER(TRIM(COALESCE(c.full_name, ''))) as full_name_normalized,
              LOWER(TRIM(c.email)) as email_normalized,
              extract_linkedin_handle(c.linkedin_url) as linkedin_handle,
              normalize_phone(c.phone_number) as phone_normalized,
              create_name_tokens(c.full_name) as name_tokens,
              LOWER(TRIM(COALESCE(c.first_name, ''))) as first_name_normalized,
              LOWER(TRIM(COALESCE(c.last_name, ''))) as last_name_normalized,
              LOWER(TRIM(COALESCE(c.personal_email, ''))) as additional_email_normalized,
              CASE WHEN c.email IS NOT NULL THEN LOWER(SUBSTRING(c.email FROM '@(.+)$')) ELSE NULL END as email_domain,
              LOWER(TRIM(COALESCE(c.company_name, ''))) as company_name_normalized,
              LOWER(TRIM(COALESCE(c.title, ''))) as title_normalized,
              CURRENT_TIMESTAMP as created_at,
              CURRENT_TIMESTAMP as updated_at
            FROM contacts c
            ORDER BY c.contact_id
            LIMIT $1 OFFSET $2
            ON CONFLICT (contact_id) DO UPDATE SET
              full_name_normalized = EXCLUDED.full_name_normalized,
              email_normalized = EXCLUDED.email_normalized,
              linkedin_handle = EXCLUDED.linkedin_handle,
              phone_normalized = EXCLUDED.phone_normalized,
              name_tokens = EXCLUDED.name_tokens,
              first_name_normalized = EXCLUDED.first_name_normalized,
              last_name_normalized = EXCLUDED.last_name_normalized,
              additional_email_normalized = EXCLUDED.additional_email_normalized,
              email_domain = EXCLUDED.email_domain,
              company_name_normalized = EXCLUDED.company_name_normalized,
              title_normalized = EXCLUDED.title_normalized,
              updated_at = CURRENT_TIMESTAMP
          `, [batchSize, offset])

          processed += result.rowCount || 0
          console.log(`Processed batch ${Math.floor(offset/batchSize) + 1}: ${result.rowCount} records processed, total: ${processed}/${total}`)

        } catch (error) {
          console.error(`Error processing batch at offset ${offset}:`, error)
          errors++
        }
      }

    } finally {
      client.release()
    }

    return { processed, errors }
  }

  /**
   * Batch normalize all contacts (legacy method - slower)
   */
  static async normalizeAllContacts(batchSize: number = 100): Promise<{ processed: number, errors: number }> {
    // Use the fast method instead
    return this.normalizeAllContactsFast(batchSize * 10)
  }
}
