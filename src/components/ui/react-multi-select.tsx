'use client'

import React from 'react'
import Select, { components, MultiValueProps, OptionProps, GroupBase } from 'react-select'
import { cn } from '@/lib/utils'
import { CheckSquare, Square, Minus } from 'lucide-react'

interface Option {
  value: string
  label: string
  isSelectAll?: boolean
  isAddNew?: boolean
}

interface ReactMultiSelectProps {
  options: Option[]
  selected: string[]
  onChange: (selected: string[]) => void
  placeholder?: string
  disabled?: boolean
  className?: string
  showSelectAll?: boolean
  selectAllLabel?: string
  isSearchable?: boolean
  allowCustomValues?: boolean
  onCustomValueAdd?: (value: string) => void
}

export function ReactMultiSelect({
  options,
  selected,
  onChange,
  placeholder = "Select...",
  disabled = false,
  className,
  showSelectAll = true,
  selectAllLabel = "Select All",
  isSearchable = true,
  allowCustomValues = false,
  onCustomValueAdd
}: ReactMultiSelectProps) {
  const [inputValue, setInputValue] = React.useState('')
  
  // Filter options based on search input
  const filteredOptions = React.useMemo(() => {
    if (!options || !Array.isArray(options)) return []
    if (!inputValue || !isSearchable) return options
    return options.filter(option => 
      option.label.toLowerCase().includes(inputValue.toLowerCase()) ||
      option.value.toLowerCase().includes(inputValue.toLowerCase())
    )
  }, [options, inputValue, isSearchable])
  
  // Create options with Select All option and add new option
  const allOptions = React.useMemo(() => {
    const optionsToUse = filteredOptions || []
    
    // Add "Add new" option if enabled and input value doesn't match existing options
    const addNewOption = allowCustomValues && inputValue.trim() && 
      !optionsToUse.some(option => 
        option.value.toLowerCase() === inputValue.toLowerCase() ||
        option.label.toLowerCase() === inputValue.toLowerCase()
      ) ? {
        value: `__ADD_NEW__${inputValue}`,
        label: `Add "${inputValue}"`,
        isAddNew: true
      } : null
    
    // Build options array
    const finalOptions: Option[] = []
    
    // Add Select All if enabled
    if (showSelectAll && optionsToUse.length > 0) {
      const remainingOptions = optionsToUse.filter(option => !selected.includes(option.value))
      const remainingCount = remainingOptions.length
      
      finalOptions.push({
        value: '__SELECT_ALL__',
        label: remainingCount > 0 ? `Select All (${remainingCount} remaining)` : `All Selected`,
        isSelectAll: true
      })
    }
    
    // Add "Add new" option
    if (addNewOption) {
      finalOptions.push(addNewOption)
    }
    
    // Add regular options
    finalOptions.push(...optionsToUse)
    
    // Debug logging for options with "nation" in the name
    const nationOptions = finalOptions.filter(opt => opt.label.toLowerCase().includes('nation'))
    if (nationOptions.length > 0) {
      console.log('🔍 ReactMultiSelect - Nation options found:', {
        placeholder,
        nationOptions: nationOptions.map(opt => ({ 
          value: opt.value, 
          label: opt.label, 
          isSelectAll: opt.isSelectAll,
          isAddNew: opt.isAddNew 
        })),
        totalOptions: finalOptions.length,
        inputValue,
        showSelectAll,
        allowCustomValues
      })
    }
    
    return finalOptions
  }, [filteredOptions, showSelectAll, selectAllLabel, inputValue, selected, allowCustomValues, placeholder])

  // Determine select all state (based on visible/filtered options)
  const visibleOptions = filteredOptions || []

  // Custom Option component to handle Select All and Custom Values
  const CustomOption = (props: OptionProps<Option, true, GroupBase<Option>>) => {
    const { data, isSelected } = props
    
    if (data.isSelectAll) {
      // Calculate remaining unselected options in visible list
      const remainingOptions = visibleOptions.filter(option => !selected.includes(option.value))
      const hasRemainingOptions = remainingOptions.length > 0
      
      return (
        <components.Option {...props}>
          <div className="flex items-center gap-2">
            {hasRemainingOptions ? (
              <Square className="h-4 w-4 text-gray-400" />
            ) : (
              <CheckSquare className="h-4 w-4 text-blue-600" />
            )}
            <span className="font-medium text-blue-700">{data.label}</span>
          </div>
        </components.Option>
      )
    }

    if (data.isAddNew) {
      return (
        <components.Option {...props}>
          <div className="flex items-center gap-2">
            <div className="h-4 w-4 rounded-full bg-green-100 border-2 border-green-300 flex items-center justify-center">
              <span className="text-green-600 text-xs">+</span>
            </div>
            <span className="font-medium text-green-700">{data.label}</span>
          </div>
        </components.Option>
      )
    }

    return (
      <components.Option {...props}>
        <div className="flex items-center gap-2">
          {isSelected ? (
            <CheckSquare className="h-4 w-4 text-blue-600" />
          ) : (
            <Square className="h-4 w-4 text-gray-400" />
          )}
          <span>{data.label}</span>
        </div>
      </components.Option>
    )
  }

  // Custom MultiValue component to prevent Select All and Add New options from being displayed as selected values
  const CustomMultiValue = (props: MultiValueProps<Option, true, GroupBase<Option>>) => {
    if (props.data.isSelectAll || props.data.isAddNew) {
      return null
    }
    return <components.MultiValue {...props} />
  }

  const handleChange = (newValue: any) => {
    if (!newValue) {
      onChange([])
      return
    }

    const hasSelectAll = newValue.some((option: Option) => option.isSelectAll)
    const hasAddNew = newValue.some((option: Option) => option.isAddNew)
    
    if (hasSelectAll) {
      // Select all visible options
      const remainingOptions = visibleOptions.filter(option => !selected.includes(option.value))
      
      if (remainingOptions.length === 0) {
        // Deselect all visible options
        const currentlySelected = new Set(selected)
        visibleOptions.forEach(option => currentlySelected.delete(option.value))
        onChange(Array.from(currentlySelected))
      } else {
        // Select all remaining options
        const currentlySelected = new Set(selected)
        remainingOptions.forEach(option => currentlySelected.add(option.value))
        onChange(Array.from(currentlySelected))
      }
    } else if (hasAddNew) {
      // Handle add new option
      const addNewOption = newValue.find((option: Option) => option.isAddNew)
      if (addNewOption) {
        const newValue = addNewOption.value.replace('__ADD_NEW__', '')
        
        // Add the new value to the selection
        const newSelected = [...selected, newValue]
        onChange(newSelected)
        
        // Call the callback if provided
        if (onCustomValueAdd) {
          onCustomValueAdd(newValue)
        }
        
        // Clear the input
        setInputValue('')
      }
    } else {
      // Normal selection - just get the values
      const normalOptions = newValue.filter((option: Option) => !option.isSelectAll && !option.isAddNew)
      const values = normalOptions.map((option: Option) => option.value)
      onChange(values)
    }
  }

  // Create display value from selected values
  const displayValue = React.useMemo(() => {
    if (!selected || selected.length === 0) return []
    
    return selected.map(value => {
      // Try to find the option in the options array
      const matchingOption = options?.find(option => option.value === value)
      
      if (matchingOption) {
        return {
          value: matchingOption.value,
          label: matchingOption.label
        }
      } else {
        // If not found, create a simple option (could be a custom value)
        return {
          value: value,
          label: value
        }
      }
    })
  }, [selected, options])

  return (
    <div className={cn("w-full", className)}>
      <Select
        isMulti
        value={displayValue}
        onChange={handleChange}
        onInputChange={setInputValue}
        inputValue={inputValue}
        options={allOptions}
        placeholder={placeholder}
        isDisabled={disabled}
        isSearchable={isSearchable}
        className="react-select-container"
        classNamePrefix="react-select"
        components={{
          Option: CustomOption,
          MultiValue: CustomMultiValue
        }}
        styles={{
          control: (provided, state) => ({
            ...provided,
            borderColor: state.isFocused ? '#3b82f6' : '#d1d5db',
            boxShadow: state.isFocused ? '0 0 0 1px #3b82f6' : 'none',
            '&:hover': {
              borderColor: state.isFocused ? '#3b82f6' : '#9ca3af'
            },
            minHeight: '40px',
            backgroundColor: 'white'
          }),
          multiValue: (provided) => ({
            ...provided,
            backgroundColor: '#eff6ff',
            border: '1px solid #dbeafe'
          }),
          multiValueLabel: (provided) => ({
            ...provided,
            color: '#1e40af',
            fontSize: '0.875rem'
          }),
          multiValueRemove: (provided) => ({
            ...provided,
            color: '#64748b',
            ':hover': {
              backgroundColor: '#fecaca',
              color: '#dc2626'
            }
          }),
          menu: (provided) => ({
            ...provided,
            zIndex: 9999,
            maxHeight: '300px',
            overflow: 'hidden'
          }),
          menuList: (provided) => ({
            ...provided,
            maxHeight: '300px',
            overflow: 'auto'
          }),
          menuPortal: (provided) => ({
            ...provided,
            zIndex: 9999
          }),
          option: (provided, state) => ({
            ...provided,
            backgroundColor: state.isSelected 
              ? '#dbeafe' 
              : state.isFocused 
                ? '#f0f9ff' 
                : 'white',
            color: state.isSelected ? '#1e40af' : '#374151',
            ':hover': {
              backgroundColor: '#f0f9ff'
            }
          })
        }}
        menuPortalTarget={typeof document !== 'undefined' ? document.body : undefined}
        hideSelectedOptions={false}
        closeMenuOnSelect={false}
        blurInputOnSelect={false}
      />
    </div>
  )
} 