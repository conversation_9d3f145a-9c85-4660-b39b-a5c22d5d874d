"use client"

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import {
  Users,
  Shield,
  Key,
  Search,
  Plus,
  Edit,
  Eye,
  EyeOff,
  UserCheck,
  UserX,
  Activity,
  AlertTriangle,
  Clock,
  MoreHorizontal,
  LayoutDashboard,
  Trash2
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger,
  DialogFooter
} from '@/components/ui/dialog';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from '@/components/ui/dropdown-menu';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { toast } from 'sonner';

// Types
interface User {
  user_id: number;
  first_name: string;
  last_name: string;
  full_name: string;
  email: string;
  username?: string;
  role: string;
  is_admin: boolean;
  is_active: boolean;
  permissions: string[];
  last_login?: string;
  login_count: number;
  failed_login_attempts: number;
  password_changed_at: string;
  force_password_change: boolean;
  impersonated_by?: number;
  created_at: string;
  updated_at: string;
  phone?: string;
  notes?: string;
  tags: string[];
}

interface UserActivity {
  activity_id?: number;
  user_id?: number;
  user_name?: string;
  action: string;
  description: string;
  timestamp: string;
  created_at?: string;
  ip_address?: string;
  user_agent?: string;
  performed_by_name?: string;
  impersonation_active?: boolean;
}

interface ImpersonationSession {
  user_id: number;
  full_name: string;
  email: string;
  role: string;
  impersonation_started_at: string;
}

// Main AdminPanel Component
export default function AdminPanel() {
  const router = useRouter();
  const { data: session } = useSession();
  const [activeTab, setActiveTab] = useState('users');
  const [users, setUsers] = useState<User[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<User[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 50,
    total: 0,
    totalPages: 0
  });
  const [impersonationSessions, setImpersonationSessions] = useState<ImpersonationSession[]>([]);

  // Load users
  useEffect(() => {
    loadUsers();
    loadImpersonationSessions();
  }, [pagination.page, searchTerm, roleFilter, statusFilter]);

  // Filter users
  useEffect(() => {
    let filtered = users;
    
    if (searchTerm) {
      filtered = filtered.filter(user => 
        user.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.username?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }
    
    if (roleFilter !== 'all') {
      filtered = filtered.filter(user => user.role === roleFilter);
    }
    
    if (statusFilter === 'active') {
      filtered = filtered.filter(user => user.is_active);
    } else if (statusFilter === 'inactive') {
      filtered = filtered.filter(user => !user.is_active);
    }
    
    setFilteredUsers(filtered);
  }, [users, searchTerm, roleFilter, statusFilter]);

  const loadUsers = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        search: searchTerm,
        role: roleFilter !== 'all' ? roleFilter : '',
        status: statusFilter !== 'all' ? statusFilter : ''
      });

      const response = await fetch(`/api/admin/users?${params}`);
      const data = await response.json();

      if (response.ok) {
        setUsers(data.users);
        setPagination(data.pagination);
      } else {
        toast.error(data.error || "Failed to load users");
      }
    } catch (error) {
      toast.error("Failed to load users");
    } finally {
      setLoading(false);
    }
  };

  const loadImpersonationSessions = async () => {
    try {
      const response = await fetch('/api/admin/impersonate');
      const data = await response.json();

      if (response.ok) {
        setImpersonationSessions(data.activeImpersonations);
      }
    } catch (error) {
      console.error('Failed to load impersonation sessions:', error);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case 'admin': return 'bg-red-100 text-red-800';
      case 'manager': return 'bg-blue-100 text-blue-800';
      case 'user': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Admin Panel</h1>
          <p className="text-gray-600">Manage users, permissions, and system access</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => router.push('/dashboard')}>
            <LayoutDashboard className="w-4 h-4 mr-2" />
            Dashboard
          </Button>
          <CreateUserDialog onUserCreated={loadUsers} />
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="users">
            <Users className="w-4 h-4 mr-2" />
            User Management
          </TabsTrigger>
          <TabsTrigger value="impersonation">
            <Shield className="w-4 h-4 mr-2" />
            Active Impersonations
            {impersonationSessions.length > 0 && (
              <Badge variant="secondary" className="ml-2">
                {impersonationSessions.length}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="activity">
            <Activity className="w-4 h-4 mr-2" />
            Activity Log
          </TabsTrigger>
        </TabsList>

        <TabsContent value="users" className="mt-6">
          <UsersTab 
            users={filteredUsers}
            loading={loading}
            searchTerm={searchTerm}
            setSearchTerm={setSearchTerm}
            roleFilter={roleFilter}
            setRoleFilter={setRoleFilter}
            statusFilter={statusFilter}
            setStatusFilter={setStatusFilter}
            pagination={pagination}
            setPagination={setPagination}
            onUserUpdated={loadUsers}
            getRoleBadgeColor={getRoleBadgeColor}
            formatDate={formatDate}
            currentUser={session?.user}
          />
        </TabsContent>

        <TabsContent value="impersonation" className="mt-6">
          <ImpersonationTab 
            sessions={impersonationSessions}
            onSessionEnded={loadImpersonationSessions}
            formatDate={formatDate}
          />
        </TabsContent>

        <TabsContent value="activity" className="mt-6">
          <ActivityTab />
        </TabsContent>
      </Tabs>
    </div>
  );
}

// Users Tab Component
function UsersTab({ 
  users, 
  loading, 
  searchTerm, 
  setSearchTerm,
  roleFilter,
  setRoleFilter,
  statusFilter,
  setStatusFilter,
  pagination,
  setPagination,
  onUserUpdated,
  getRoleBadgeColor,
  formatDate,
  currentUser
}: any) {
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Users</CardTitle>
          <div className="flex gap-2">
            <div className="flex items-center gap-2">
              <Search className="w-4 h-4 text-gray-400" />
              <Input
                placeholder="Search users..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-64"
              />
            </div>
            <Select value={roleFilter} onValueChange={setRoleFilter}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Role" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Roles</SelectItem>
                <SelectItem value="admin">Admin</SelectItem>
                <SelectItem value="manager">Manager</SelectItem>
                <SelectItem value="user">User</SelectItem>
                <SelectItem value="guest">Guest</SelectItem>
              </SelectContent>
            </Select>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        ) : (
          <UsersList 
            users={users}
            onUserUpdated={onUserUpdated}
            getRoleBadgeColor={getRoleBadgeColor}
            formatDate={formatDate}
            currentUser={currentUser}
          />
        )}
      </CardContent>
    </Card>
  );
}

// Users List Component
function UsersList({ users, onUserUpdated, getRoleBadgeColor, formatDate, currentUser }: any) {
  return (
    <div className="space-y-4">
      {users.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          No users found matching your criteria
        </div>
      ) : (
        users.map((user: User) => (
          <UserCard 
            key={user.user_id} 
            user={user} 
            onUserUpdated={onUserUpdated}
            getRoleBadgeColor={getRoleBadgeColor}
            formatDate={formatDate}
            currentUser={currentUser}
          />
        ))
      )}
    </div>
  );
}

// Individual User Card
function UserCard({ user, onUserUpdated, getRoleBadgeColor, formatDate, currentUser }: any) {
  const [showDetails, setShowDetails] = useState(false);

  const handleImpersonate = async () => {
    try {
      const response = await fetch('/api/admin/impersonate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          targetUserId: user.user_id,
          reason: 'Admin impersonation for support purposes' 
        })
      });

      const data = await response.json();

      if (response.ok) {
        // Store impersonation token and redirect
        localStorage.setItem('impersonation_token', data.impersonationToken);
        toast.success(`Now impersonating ${user.full_name}`);
        // Refresh the page to apply impersonation
        window.location.reload();
      } else {
        toast.error(data.error || "Failed to start impersonation");
      }
    } catch (error) {
      toast.error("Failed to start impersonation");
    }
  };

  const handleToggleStatus = async () => {
    try {
      const response = await fetch(`/api/admin/users/${user.user_id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ is_active: !user.is_active })
      });

      const data = await response.json();

      if (response.ok) {
        toast.success(`User ${user.is_active ? 'deactivated' : 'activated'} successfully`);
        onUserUpdated();
      } else {
        toast.error(data.error || "Failed to update user");
      }
    } catch (error) {
      toast.error("Failed to update user");
    }
  };

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex-shrink-0">
              <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                <span className="text-blue-600 font-medium">
                  {user.first_name[0]}{user.last_name[0]}
                </span>
              </div>
            </div>
            <div>
              <h3 className="font-semibold text-gray-900">{user.full_name}</h3>
              <p className="text-sm text-gray-600">{user.email}</p>
              <div className="flex items-center gap-2 mt-1">
                <Badge className={getRoleBadgeColor(user.role)}>
                  {user.role}
                </Badge>
                <Badge variant={user.is_active ? "default" : "secondary"}>
                  {user.is_active ? "Active" : "Inactive"}
                </Badge>
                {user.is_admin && (
                  <Badge variant="destructive">
                    <Shield className="w-3 h-3 mr-1" />
                    Admin
                  </Badge>
                )}
                {user.impersonated_by && (
                  <Badge variant="outline" className="text-orange-600">
                    <Eye className="w-3 h-3 mr-1" />
                    Being Impersonated
                  </Badge>
                )}
              </div>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowDetails(!showDetails)}
            >
              {showDetails ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
            </Button>
            {/* Test delete button outside dropdown */}
            {currentUser?.user_id !== user.user_id && (
              <DeleteUserDialog user={user} onUserDeleted={onUserUpdated} />
            )}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm">
                  <MoreHorizontal className="w-4 h-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <EditUserDialog user={user} onUserUpdated={onUserUpdated} />
                <ChangePasswordDialog user={user} />
                <DropdownMenuSeparator />
                {/* Prevent impersonating yourself */}
                {currentUser?.user_id !== user.user_id && (
                  <DropdownMenuItem 
                    onClick={handleImpersonate} 
                    className={user.impersonated_by ? "opacity-50 cursor-not-allowed" : ""}
                  >
                    <UserCheck className="w-4 h-4 mr-2" />
                    Impersonate
                  </DropdownMenuItem>
                )}
                {/* Prevent deactivating yourself */}
                {currentUser?.user_id !== user.user_id && (
                  <DropdownMenuItem onClick={handleToggleStatus}>
                    {user.is_active ? (
                      <>
                        <UserX className="w-4 h-4 mr-2" />
                        Deactivate
                      </>
                    ) : (
                      <>
                        <UserCheck className="w-4 h-4 mr-2" />
                        Activate
                      </>
                    )}
                  </DropdownMenuItem>
                )}
                {/* Delete User - Only show for other users */}
                {currentUser?.user_id !== user.user_id && (
                  <DeleteUserDialog user={user} onUserDeleted={onUserUpdated} />
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {showDetails && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium text-gray-500">Last Login:</span>
                <p>{user.last_login ? formatDate(user.last_login) : 'Never'}</p>
              </div>
              <div>
                <span className="font-medium text-gray-500">Login Count:</span>
                <p>{user.login_count}</p>
              </div>
              <div>
                <span className="font-medium text-gray-500">Failed Attempts:</span>
                <p className={user.failed_login_attempts > 0 ? "text-red-600" : ""}>
                  {user.failed_login_attempts}
                </p>
              </div>
              <div>
                <span className="font-medium text-gray-500">Password Changed:</span>
                <p>{formatDate(user.password_changed_at)}</p>
              </div>
              {user.phone && (
                <div>
                  <span className="font-medium text-gray-500">Phone:</span>
                  <p>{user.phone}</p>
                </div>
              )}
              <div>
                <span className="font-medium text-gray-500">Created:</span>
                <p>{formatDate(user.created_at)}</p>
              </div>
            </div>
            {user.notes && (
              <div className="mt-4">
                <span className="font-medium text-gray-500">Notes:</span>
                <p className="mt-1 text-gray-700">{user.notes}</p>
              </div>
            )}
            {user.tags && user.tags.length > 0 && (
              <div className="mt-4">
                <span className="font-medium text-gray-500">Tags:</span>
                <div className="flex flex-wrap gap-1 mt-1">
                  {user.tags.map((tag, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// Create User Dialog Component
function CreateUserDialog({ onUserCreated }: { onUserCreated: () => void }) {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    first_name: '',
    last_name: '',
    email: '',
    username: '',
    password: '',
    role: 'user',
    is_admin: false,
    is_active: true,
    phone: '',
    notes: '',
    force_password_change: true
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const response = await fetch('/api/admin/users', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      });

      const data = await response.json();

      if (response.ok) {
        toast.success("User created successfully");
        setOpen(false);
        setFormData({
          first_name: '',
          last_name: '',
          email: '',
          username: '',
          password: '',
          role: 'user',
          is_admin: false,
          is_active: true,
          phone: '',
          notes: '',
          force_password_change: true
        });
        onUserCreated();
      } else {
        toast.error(data.error || "Failed to create user");
      }
    } catch (error) {
      toast.error("Failed to create user");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button>
          <Plus className="w-4 h-4 mr-2" />
          Add User
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Create New User</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-2">
            <div>
              <Label htmlFor="first_name">First Name</Label>
              <Input
                id="first_name"
                value={formData.first_name}
                onChange={(e) => setFormData({...formData, first_name: e.target.value})}
                required
              />
            </div>
            <div>
              <Label htmlFor="last_name">Last Name</Label>
              <Input
                id="last_name"
                value={formData.last_name}
                onChange={(e) => setFormData({...formData, last_name: e.target.value})}
                required
              />
            </div>
          </div>
          <div>
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              value={formData.email}
              onChange={(e) => setFormData({...formData, email: e.target.value})}
              required
            />
          </div>
          <div>
            <Label htmlFor="username">Username (Optional)</Label>
            <Input
              id="username"
              value={formData.username}
              onChange={(e) => setFormData({...formData, username: e.target.value})}
            />
          </div>
          <div>
            <Label htmlFor="password">Password</Label>
            <Input
              id="password"
              type="password"
              value={formData.password}
              onChange={(e) => setFormData({...formData, password: e.target.value})}
              required
              minLength={8}
            />
          </div>
          <div>
            <Label htmlFor="role">Role</Label>
            <Select value={formData.role} onValueChange={(value) => setFormData({...formData, role: value})}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="user">User</SelectItem>
                <SelectItem value="manager">Manager</SelectItem>
                <SelectItem value="admin">Admin</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label htmlFor="phone">Phone (Optional)</Label>
            <Input
              id="phone"
              value={formData.phone}
              onChange={(e) => setFormData({...formData, phone: e.target.value})}
            />
          </div>
          <div className="flex items-center space-x-2">
            <Switch
              id="is_admin"
              checked={formData.is_admin}
              onCheckedChange={(checked: boolean) => setFormData({...formData, is_admin: checked})}
            />
            <Label htmlFor="is_admin">Admin privileges</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Switch
              id="force_password_change"
              checked={formData.force_password_change}
              onCheckedChange={(checked: boolean) => setFormData({...formData, force_password_change: checked})}
            />
            <Label htmlFor="force_password_change">Force password change on first login</Label>
          </div>
          <div>
            <Label htmlFor="notes">Notes (Optional)</Label>
            <Textarea
              id="notes"
              value={formData.notes}
              onChange={(e) => setFormData({...formData, notes: e.target.value})}
              rows={2}
            />
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => setOpen(false)}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? 'Creating...' : 'Create User'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}

// Edit User Dialog Component (simplified for space)
function EditUserDialog({ user, onUserUpdated }: { user: User; onUserUpdated: () => void }) {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    first_name: user.first_name || '',
    last_name: user.last_name || '',
    email: user.email || '',
    username: user.username || '',
    role: user.role || 'user',
    is_admin: user.is_admin || false,
    is_active: user.is_active || true,
    phone: user.phone || '',
    notes: user.notes || '',
    force_password_change: user.force_password_change || false
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const response = await fetch(`/api/admin/users/${user.user_id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      });

      const data = await response.json();

      if (response.ok) {
        toast.success("User updated successfully");
        setOpen(false);
        onUserUpdated();
      } else {
        toast.error(data.error || "Failed to update user");
      }
    } catch (error) {
      toast.error("Failed to update user");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <DropdownMenuItem 
          onSelect={(e) => {
            e.preventDefault();
            setOpen(true);
          }}
        >
          <Edit className="w-4 h-4 mr-2" />
          Edit User
        </DropdownMenuItem>
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit User: {user.full_name}</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="first_name">First Name</Label>
              <Input
                id="first_name"
                value={formData.first_name}
                onChange={(e) => setFormData({ ...formData, first_name: e.target.value })}
                required
              />
            </div>
            <div>
              <Label htmlFor="last_name">Last Name</Label>
              <Input
                id="last_name"
                value={formData.last_name}
                onChange={(e) => setFormData({ ...formData, last_name: e.target.value })}
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                required
              />
            </div>
            <div>
              <Label htmlFor="username">Username</Label>
              <Input
                id="username"
                value={formData.username}
                onChange={(e) => setFormData({ ...formData, username: e.target.value })}
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="role">Role</Label>
              <Select value={formData.role} onValueChange={(value) => setFormData({ ...formData, role: value })}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="user">User</SelectItem>
                  <SelectItem value="manager">Manager</SelectItem>
                  <SelectItem value="admin">Admin</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="phone">Phone</Label>
              <Input
                id="phone"
                value={formData.phone}
                onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
              />
            </div>
          </div>

          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Switch
                id="is_admin"
                checked={formData.is_admin}
                onCheckedChange={(checked: boolean) => setFormData({ ...formData, is_admin: checked })}
              />
              <Label htmlFor="is_admin">Admin User</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="is_active"
                checked={formData.is_active}
                onCheckedChange={(checked: boolean) => setFormData({ ...formData, is_active: checked as any })}
              />
              <Label htmlFor="is_active">Active User</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="force_password_change"
                checked={formData.force_password_change}
                onCheckedChange={(checked: boolean) => setFormData({ ...formData, force_password_change: checked })}
              />
              <Label htmlFor="force_password_change">Force Password Change</Label>
            </div>
          </div>

          <div>
            <Label htmlFor="notes">Notes</Label>
            <Textarea
              id="notes"
              value={formData.notes}
              onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
              rows={3}
            />
          </div>

          <DialogFooter>
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => setOpen(false)}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? 'Updating...' : 'Update User'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}

// Change Password Dialog Component
function ChangePasswordDialog({ user }: { user: User }) {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    newPassword: '',
    confirmPassword: '',
    forceChange: true
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (formData.newPassword !== formData.confirmPassword) {
        toast.error("Passwords do not match");
      return;
    }

    if (formData.newPassword.length < 8) {
      toast.error("Password must be at least 8 characters long");
      return;
    }

    setLoading(true);

    try {
      const response = await fetch(`/api/admin/users/${user.user_id}/password`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          newPassword: formData.newPassword,
          forcePasswordChange: formData.forceChange
        })
      });

      const data = await response.json();

      if (response.ok) {
        toast.success("Password updated successfully");
        setOpen(false);
        setFormData({
          newPassword: '',
          confirmPassword: '',
          forceChange: true
        });
      } else {
        toast.error(data.error || "Failed to update password");
      }
    } catch (error) {
      toast.error("Failed to update password");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <DropdownMenuItem 
          onSelect={(e) => {
            e.preventDefault();
            setOpen(true);
          }}
        >
          <Key className="w-4 h-4 mr-2" />
          Change Password
        </DropdownMenuItem>
      </DialogTrigger>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Change Password for {user.full_name}</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="newPassword">New Password</Label>
            <Input
              id="newPassword"
              type="password"
              value={formData.newPassword}
              onChange={(e) => setFormData({ ...formData, newPassword: e.target.value })}
              required
              minLength={8}
            />
          </div>
          
          <div>
            <Label htmlFor="confirmPassword">Confirm Password</Label>
            <Input
              id="confirmPassword"
              type="password"
              value={formData.confirmPassword}
              onChange={(e) => setFormData({ ...formData, confirmPassword: e.target.value })}
              required
              minLength={8}
            />
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              id="forceChange"
              checked={formData.forceChange}
              onCheckedChange={(checked: boolean) => setFormData({ ...formData, forceChange: checked })}
            />
            <Label htmlFor="forceChange">Force user to change password on next login</Label>
          </div>

          <DialogFooter>
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => setOpen(false)}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? 'Updating...' : 'Update Password'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}

// Delete User Dialog Component
function DeleteUserDialog({ user, onUserDeleted }: { user: User; onUserDeleted: () => void }) {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [confirmText, setConfirmText] = useState('');

  const expectedConfirmText = `DELETE ${user.email}`;

  console.log('DeleteUserDialog rendered for user:', user.email, 'open:', open);

  const handleDelete = async () => {
    if (confirmText !== expectedConfirmText) {
      toast.error("Confirmation text doesn't match");
      return;
    }

    setLoading(true);

    try {
      const response = await fetch(`/api/admin/users/${user.user_id}`, {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' }
      });

      const data = await response.json();

      if (response.ok) {
        toast.success(`User ${user.full_name} deleted successfully`);
        setOpen(false);
        setConfirmText('');
        onUserDeleted();
      } else {
        toast.error(data.error || "Failed to delete user");
      }
    } catch (error) {
      toast.error("Failed to delete user");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <DropdownMenuItem
          onSelect={(e) => {
            console.log('Delete user clicked for:', user.email);
            e.preventDefault();
            setOpen(true);
            console.log('Dialog should be open now');
          }}
          className="text-red-600 focus:text-red-600"
        >
          <Trash2 className="w-4 h-4 mr-2" />
          Delete User
        </DropdownMenuItem>
      </DialogTrigger>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Delete User: {user.full_name}</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <p>Are you sure you want to delete this user?</p>
            <p><strong>Email:</strong> {user.email}</p>
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setOpen(false)}
            >
              Cancel
            </Button>
            <Button
              type="button"
              variant="destructive"
              onClick={() => {
                console.log('Delete button clicked');
                setOpen(false);
              }}
            >
              Delete User
            </Button>
          </DialogFooter>
        </DialogContent>
    </Dialog>
  );
}


// Impersonation Tab Component (simplified)
function ImpersonationTab({ sessions, onSessionEnded, formatDate }: any) {
  const [loading, setLoading] = useState<number | null>(null);

  const handleEndImpersonation = async (session: ImpersonationSession) => {
    setLoading(session.user_id);
    
    try {
      const response = await fetch('/api/admin/impersonate', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ targetUserId: session.user_id })
      });

      const data = await response.json();

      if (response.ok) {
        toast.success(`Impersonation of ${session.full_name} ended successfully`);
        onSessionEnded(); // Refresh the sessions list
      } else {
        toast.error(data.error || "Failed to end impersonation");
      }
    } catch (error) {
      toast.error("Failed to end impersonation");
    } finally {
      setLoading(null);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Active Impersonation Sessions</CardTitle>
        <p className="text-sm text-gray-600">
          Manage ongoing impersonation sessions. Only administrators can end impersonations.
        </p>
      </CardHeader>
      <CardContent>
        {sessions.length === 0 ? (
          <div className="text-center py-8">
            <Shield className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">No active impersonation sessions</p>
            <p className="text-sm text-gray-400 mt-2">All impersonation sessions have been ended</p>
          </div>
        ) : (
          <div className="space-y-4">
            {sessions.map((session: ImpersonationSession) => (
              <div key={session.user_id} className="flex items-center justify-between p-4 border rounded-lg bg-yellow-50 border-yellow-200">
                <div className="flex items-center space-x-4">
                  <div className="flex-shrink-0">
                    <div className="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center">
                      <Shield className="w-5 h-5 text-yellow-600" />
                    </div>
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900">{session.full_name}</h3>
                    <p className="text-sm text-gray-600">{session.email}</p>
                    <div className="flex items-center space-x-4 mt-1">
                      <span className="text-xs text-gray-500">
                        Started: {formatDate(session.impersonation_started_at)}
                      </span>
                      <Badge variant="secondary" className="text-xs">
                        {session.role}
                      </Badge>
                    </div>
                  </div>
                </div>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => handleEndImpersonation(session)}
                  disabled={loading === session.user_id}
                  className="border-red-200 text-red-600 hover:bg-red-50"
                >
                  {loading === session.user_id ? (
                    <>
                      <Clock className="w-4 h-4 mr-2 animate-spin" />
                      Ending...
                    </>
                  ) : (
                    <>
                      <UserX className="w-4 h-4 mr-2" />
                      End Impersonation
                    </>
                  )}
                </Button>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// Activity Tab Component
function ActivityTab() {
  const [activities, setActivities] = useState<UserActivity[]>([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 50,
    total: 0,
    totalPages: 0
  });
  const [filter, setFilter] = useState({
    action: 'all',
    userId: 'all',
    dateRange: '7' // days
  });

  const loadActivities = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        action: filter.action !== 'all' ? filter.action : '',
        userId: filter.userId !== 'all' ? filter.userId : '',
        days: filter.dateRange
      });

      const response = await fetch(`/api/admin/users/activity?${params}`);
      const data = await response.json();

      if (response.ok) {
        setActivities(data.activities || []);
        setPagination(data.pagination || pagination);
      } else {
        toast.error(data.error || "Failed to load activity log");
      }
    } catch (error) {
      toast.error("Failed to load activity log");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadActivities();
  }, [pagination.page, filter]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getActionIcon = (action: string) => {
    switch (action.toLowerCase()) {
      case 'login': return <UserCheck className="w-4 h-4 text-green-600" />;
      case 'logout': return <UserX className="w-4 h-4 text-gray-600" />;
      case 'impersonate': return <Shield className="w-4 h-4 text-blue-600" />;
      case 'password_change': return <Key className="w-4 h-4 text-orange-600" />;
      case 'profile_update': return <Edit className="w-4 h-4 text-purple-600" />;
      default: return <Activity className="w-4 h-4 text-gray-600" />;
    }
  };

  const getActionBadgeColor = (action: string) => {
    switch (action.toLowerCase()) {
      case 'login': return 'bg-green-100 text-green-800';
      case 'logout': return 'bg-gray-100 text-gray-800';
      case 'impersonate': return 'bg-blue-100 text-blue-800';
      case 'password_change': return 'bg-orange-100 text-orange-800';
      case 'profile_update': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>User Activity Log</CardTitle>
        <p className="text-sm text-gray-600">
          Monitor user activities and system access patterns
        </p>
      </CardHeader>
      <CardContent>
        {/* Filters */}
        <div className="flex flex-wrap gap-4 mb-6">
          <div>
            <Label htmlFor="action-filter">Action</Label>
            <Select value={filter.action} onValueChange={(value) => setFilter({ ...filter, action: value })}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Actions</SelectItem>
                <SelectItem value="login">Login</SelectItem>
                <SelectItem value="logout">Logout</SelectItem>
                <SelectItem value="impersonate">Impersonation</SelectItem>
                <SelectItem value="password_change">Password Change</SelectItem>
                <SelectItem value="profile_update">Profile Update</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label htmlFor="date-filter">Time Range</Label>
            <Select value={filter.dateRange} onValueChange={(value) => setFilter({ ...filter, dateRange: value })}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1">Last 24h</SelectItem>
                <SelectItem value="7">Last 7 days</SelectItem>
                <SelectItem value="30">Last 30 days</SelectItem>
                <SelectItem value="90">Last 90 days</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Activity List */}
        {loading ? (
          <div className="text-center py-8">
            <Clock className="w-8 h-8 text-gray-400 mx-auto mb-4 animate-spin" />
            <p className="text-gray-500">Loading activity log...</p>
          </div>
        ) : activities.length === 0 ? (
          <div className="text-center py-8">
            <Activity className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">No activity found</p>
            <p className="text-sm text-gray-400 mt-2">No user activities match your current filters</p>
          </div>
        ) : (
          <div className="space-y-3">
            {activities.map((activity: UserActivity, index: number) => (
              <div key={index} className="flex items-center space-x-4 p-3 border rounded-lg hover:bg-gray-50">
                <div className="flex-shrink-0">
                  {getActionIcon(activity.action)}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2 mb-1">
                    <Badge className={getActionBadgeColor(activity.action)}>
                      {activity.action}
                    </Badge>
                    <span className="text-sm text-gray-500">
                      {activity.user_name || 'System'}
                    </span>
                  </div>
                  <p className="text-sm text-gray-700">{activity.description}</p>
                  <p className="text-xs text-gray-500 mt-1">
                    {formatDate(activity.timestamp)}
                  </p>
                </div>
                {activity.ip_address && (
                  <div className="flex-shrink-0">
                    <span className="text-xs text-gray-400">
                      {activity.ip_address}
                    </span>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}

        {/* Pagination */}
        {pagination.totalPages > 1 && (
          <div className="flex items-center justify-between mt-6">
            <p className="text-sm text-gray-700">
              Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
              {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
              {pagination.total} activities
            </p>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPagination({ ...pagination, page: pagination.page - 1 })}
                disabled={pagination.page === 1}
              >
                Previous
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPagination({ ...pagination, page: pagination.page + 1 })}
                disabled={pagination.page === pagination.totalPages}
              >
                Next
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
