"use client"

import React, { useState, useEffect } from 'react'
import { Navigation } from './Navigation'
import { usePathname, useRouter } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { Loader2 } from 'lucide-react'

 // Map route paths to section names
const ROUTE_TO_SECTION = {
  '/dashboard': 'dashboard',
  '/dashboard/people': 'people',
  '/dashboard/companies': 'companies',
  '/dashboard/articles': 'articles',
  '/dashboard/data-quality': 'data-quality',
  '/dashboard/duplicates': 'duplicates',
  '/dashboard/deals': 'deals',
  '/dashboard/smartlead': 'smartlead',
  '/dashboard/processing': 'processing',
  '/dashboard/projections': 'projections',
  '/dashboard/upload': 'upload',
  '/dashboard/mapping': 'mapping',
  '/dashboard/homepage': 'homepage',
  '/dashboard/configuration': 'configuration',
  '/admin': 'admin-panel'
}

export default function Dashboard({ children }: { children?: React.ReactNode }) {
  const [isCollapsed, setIsCollapsed] = useState(false)
  const pathname = usePathname() || ''
  const router = useRouter()
  const { data: session, status } = useSession()
  
  // Extract the current section from the pathname
  const getActiveSectionFromPath = (path: string): string => {
    // Check if it's a company detail page
    if (path.match(/\/dashboard\/companies\/\d+$/)) {
      return 'companies';
    }
    
    // Check if it's the add company page
    if (path === '/dashboard/companies/add') {
      return 'companies';
    }
    
    // Check if it's a people subsection
    if (path.startsWith('/dashboard/people/')) {
      return 'people';
    }
    
    // Check other sections
    for (const [route, section] of Object.entries(ROUTE_TO_SECTION)) {
      if (path.startsWith(route)) {
        return section;
      }
    }
    
    // Default to people if no match
    return 'people';
  }
  
  const activeSection = getActiveSectionFromPath(pathname)

  // Function to navigate to a section
  const setActiveSection = (section: string) => {
    // Find the corresponding route for this section
    const route = Object.entries(ROUTE_TO_SECTION).find(([_, s]) => s === section)?.[0] || '/dashboard'
    router.push(route)
  }

  // Handle loading state
  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="flex items-center space-x-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Loading dashboard...</span>
        </div>
      </div>
    )
  }

  // Redirect to login if not authenticated (middleware should handle this but double check)
  if (status === 'unauthenticated' || !session) {
    router.push('/login')
    return null
  }

  const userRole = session.user?.role || 'user'
  
  const canAccessSection = (section: string, role: string) => {
    if (role === 'admin') return true;
    if (role === 'guest') {
      return section === 'projections';
    }
    // Regular users can access most sections except admin-only ones
    const adminOnlySections = ['configuration', 'processing', 'admin-panel']
    return !adminOnlySections.includes(section);
  };

  // Check if user can access current section
  const currentSection = getActiveSectionFromPath(pathname);
  if (!canAccessSection(currentSection, userRole)) {
    // Redirect to appropriate default page based on role
    const defaultPage = '/dashboard/people'
    router.push(defaultPage);
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation 
        activeSection={activeSection} 
        setActiveSection={setActiveSection}
        isCollapsed={isCollapsed}
        setIsCollapsed={setIsCollapsed}
        accessLevel={userRole}
        session={session}
      />
      <div className={`transition-all duration-300 ${
        isCollapsed ? 'ml-16' : 'ml-40'
      }`}>
        <div className="w-full">
          {children}
        </div>
      </div>
    </div>
  )
}