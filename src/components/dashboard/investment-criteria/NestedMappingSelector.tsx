'use client'

import React, { useState, useEffect, useMemo } from 'react'
import { Label } from "@/components/ui/label"
import { ReactMultiSelect } from '@/components/ui/react-multi-select'
import { Loader2 } from "lucide-react"
import { toast } from "sonner"

interface MappingHierarchy {
  id: number;
  values: string[];
  fullMapping: any;
}

interface MappingData {
  type: string;
  levels: string[];
  hierarchyRows: MappingHierarchy[];
  nestedData?: any; // New nested data structure
}

interface NestedMappingSelectorProps {
  mappingType: string;
  selectedValues: string[];
  onSelectionChange: (values: string[]) => void;
  label: string;
  placeholder?: string;
  disabled?: boolean;
  showSelectAll?: boolean;
  selectAllLabel?: string;
  mappingData?: MappingData | null; // Allow pre-fetched data
  allowNewOptions?: boolean; // Allow adding new options
  onAddNewOption?: (newValue: string) => void; // Callback for new options
  // New props for nested functionality
  parentMappingData?: MappingData | null; // Parent mapping data (e.g., regions for states)
  parentSelectedValues?: string[]; // Selected parent values
  parentField?: string; // Parent field name for filtering
  childField?: string; // Child field name for filtering
  isChild?: boolean; // Whether this is a child selector
}

export default function NestedMappingSelector({
  mappingType,
  selectedValues,
  onSelectionChange,
  label,
  placeholder = "Select options",
  disabled = false,
  showSelectAll = true,
  selectAllLabel = "Select All",
  mappingData: propMappingData,
  allowNewOptions = false,
  onAddNewOption,
  // New nested props
  parentMappingData,
  parentSelectedValues = [],
  parentField,
  childField,
  isChild = false
}: NestedMappingSelectorProps) {
  const [mappingData, setMappingData] = useState<MappingData | null>(propMappingData || null);
  const [loading, setLoading] = useState(!propMappingData);
  const [newOptionInput, setNewOptionInput] = useState('');
  const [showNewOptionInput, setShowNewOptionInput] = useState(false);

  useEffect(() => {
    if (propMappingData) {
      setMappingData(propMappingData);
      setLoading(false);
    } else {
      fetchMappingData();
    }
  }, [mappingType, propMappingData]);

  const getUniqueOptions = () => {
    if (!mappingData) return [];

    // Use nested data structure if available, otherwise fall back to hierarchyRows
    if (mappingData.nestedData) {
      const allValues = new Set<string>();
      
      // Extract all unique values from the nested structure
      Object.keys(mappingData.nestedData).forEach(key => {
        if (key && key.trim()) {
          allValues.add(key.trim());
        }
      });

      const uniqueOptions = Array.from(allValues).sort().map(value => ({
        value,
        label: value
      }));

      // Debug logging for U.S Regions to see what's happening
      if (mappingType === 'U.S Regions') {
        console.log('🔍 NestedMappingSelector - U.S Regions Debug (Nested Data):', {
          mappingType,
          nestedDataKeys: Object.keys(mappingData.nestedData).length,
          uniqueValues: allValues.size,
          nationwideOptions: uniqueOptions.filter(opt => opt.value.toLowerCase().includes('nation')),
          allOptions: uniqueOptions.map(opt => opt.value),
          nationwideChildren: mappingData.nestedData.Nationwide ? Object.keys(mappingData.nestedData.Nationwide).length : 0
        });
      }

      return uniqueOptions;
    }

    // Fallback to old hierarchyRows method
    const allValues = new Set<string>();
    mappingData.hierarchyRows.forEach(row => {
      row.values.forEach(value => {
        if (value && value.trim()) {
          allValues.add(value.trim());
        }
      });
    });

    const uniqueOptions = Array.from(allValues).sort().map(value => ({
      value,
      label: value
    }));

    // Debug logging for U.S Regions to see what's happening
    if (mappingType === 'U.S Regions') {
      console.log('🔍 NestedMappingSelector - U.S Regions Debug (Hierarchy Rows):', {
        mappingType,
        totalRows: mappingData.hierarchyRows.length,
        uniqueValues: allValues.size,
        nationwideOptions: uniqueOptions.filter(opt => opt.value.toLowerCase().includes('nation')),
        allOptions: uniqueOptions.map(opt => opt.value)
      });
    }

    return uniqueOptions;
  };

  const fetchMappingData = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/mapping-tables/types?type=${encodeURIComponent(mappingType)}`);

      if (!response.ok) {
        throw new Error('Failed to fetch mapping data');
      }

      const data = await response.json();

      if (data.success) {
        setMappingData(data.data);
      } else {
        throw new Error(data.error || 'Failed to load mapping data');
      }
    } catch (error) {
      console.error('Error fetching mapping data:', error);
      toast.error(`Failed to load ${label.toLowerCase()}`);
    } finally {
      setLoading(false);
    }
  };

  // Filter options based on parent selection for nested relationships
  const getFilteredOptions = useMemo(() => {
    if (!mappingData) return [];

    if (!isChild || !parentMappingData || !parentSelectedValues || parentSelectedValues.length === 0) {
      // If not a child or no parent selection, return all options
      return getUniqueOptions();
    }

    // Use nested data structure if available for better performance
    if (mappingData.nestedData && parentMappingData.nestedData) {
      const allValues = new Set<string>();
      
      // Get children from selected parents using nested data
      parentSelectedValues.forEach(parentValue => {
        if (mappingData.nestedData[parentValue]) {
          Object.keys(mappingData.nestedData[parentValue]).forEach(childValue => {
            if (childValue && childValue.trim()) {
              allValues.add(childValue.trim());
            }
          });
        }
      });

      const filteredOptions = Array.from(allValues).sort().map(value => ({
        value,
        label: value
      }));

      // Debug logging for nested filtering
      if (mappingType === 'U.S States') {
        console.log('🔍 NestedMappingSelector - States Filtering (Nested Data):', {
          mappingType,
          parentSelectedValues,
          filteredOptionsCount: filteredOptions.length,
          sampleOptions: filteredOptions.slice(0, 5).map(opt => opt.value)
        });
      }

      return filteredOptions;
    }

    // Fallback to old hierarchyRows method
    const filteredRows = mappingData.hierarchyRows.filter(row => {
      // Check if this row's parent value matches any selected parent
      if (parentField && childField && row.fullMapping) {
        // For states, the parent region is in fullMapping.value_1
        const parentValue = row.fullMapping.value_1;
        const childValue = row.values[0]; // Child value is in values[0] for level 2
        return parentSelectedValues.includes(parentValue) && childValue;
      }
      return true;
    });

    const allValues = new Set<string>();
    filteredRows.forEach(row => {
      if (childField && row.values.length >= 1) {
        const childValue = row.values[0];
        if (childValue && childValue.trim()) {
          allValues.add(childValue.trim());
        }
      } else {
        // Fallback to first value if no specific child field
        row.values.forEach(value => {
          if (value && value.trim()) {
            allValues.add(value.trim());
          }
        });
      }
    });

    return Array.from(allValues).sort().map(value => ({
      value,
      label: value
    }));
  }, [mappingData, isChild, parentMappingData, parentSelectedValues, parentField, childField]);

  const handleAddNewOption = () => {
    if (newOptionInput.trim() && onAddNewOption) {
      onAddNewOption(newOptionInput.trim());
      setNewOptionInput('');
      setShowNewOptionInput(false);
    }
  };

  // Simple render function using ReactMultiSelect
  const renderSelector = () => {
    const options = getFilteredOptions;

    return (
      <div className="space-y-2">
        <ReactMultiSelect
          options={options}
          selected={selectedValues}
          onChange={onSelectionChange}
          placeholder={placeholder}
          disabled={disabled || loading}
          showSelectAll={showSelectAll}
          selectAllLabel={selectAllLabel}
          isSearchable={true}
          allowCustomValues={allowNewOptions}
          onCustomValueAdd={onAddNewOption}
        />
      </div>
    );
  };

  if (loading) {
    return (
      <div className="space-y-3">
        <Label className="text-sm font-medium text-gray-700">{label}</Label>
        <div className="flex items-center gap-2 text-sm text-gray-500">
          <Loader2 className="h-4 w-4 animate-spin" />
          Loading {label.toLowerCase()}...
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      <Label className="text-sm font-medium text-gray-700">{label}</Label>
      {renderSelector()}
    </div>
  );
}
