'use client'

import React, { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  ChevronLeft, 
  ChevronRight, 
  FileText, 
  Loader2, 
  AlertCircle,
  Target,
  Building2,
  User,
  Briefcase,
  Plus
} from 'lucide-react'
import InvestmentCriteriaDetailViewV2 from './InvestmentCriteriaDetailViewV2'
import InvestmentCriteriaForm from './InvestmentCriteriaForm'
import BatchInvestmentCriteriaForm from './BatchInvestmentCriteriaForm'

interface InvestmentCriteriaData {
  investment_criteria_id: number;
  entity_type: string;
  entity_id: number;
  entity_name?: string;
  entity_location?: string;
  capital_position: string;
  minimum_deal_size?: number;
  maximum_deal_size?: number;
  country?: string[];
  region?: string[];
  state?: string[];
  city?: string[];
  property_types?: string[];
  property_subcategories?: string[];
  strategies?: string[];
  decision_making_process?: string;
  notes?: string;
  created_at: string;
  updated_at: string;
  
  // Relationship IDs to debt/equity records
  investment_criteria_debt_id?: number;
  investment_criteria_equity_id?: number;
  
  // Debt-specific fields
  loan_type?: string;
  loan_program?: string;
  min_loan_term?: number;
  max_loan_term?: number;
  loan_interest_rate?: number;
  loan_interest_rate_based_off_sofr?: number;
  loan_interest_rate_based_off_wsj?: number;
  loan_interest_rate_based_off_prime?: number;
  loan_interest_rate_based_off_3yt?: number;
  loan_interest_rate_based_off_5yt?: number;
  loan_interest_rate_based_off_10yt?: number;
  loan_interest_rate_based_off_30yt?: number;
  loan_to_value_min?: number;
  loan_to_value_max?: number;
  loan_to_cost_min?: number;
  loan_to_cost_max?: number;
  loan_origination_min_fee?: number;
  loan_origination_max_fee?: number;
  loan_exit_min_fee?: number;
  loan_exit_max_fee?: number;
  min_loan_dscr?: number;
  max_loan_dscr?: number;
  structured_loan_tranche?: string;
  recourse_loan?: string;
  closing_time?: number;
  debt_program_overview?: string;
  lien_position?: string;
  loan_min_debt_yield?: string;
  prepayment?: string;
  yield_maintenance?: string;
  amortization?: string;
  application_deposit?: number;
  good_faith_deposit?: number;
  future_facilities?: string;
  eligible_borrower?: string;
  occupancy_requirements?: string;
  rate_lock?: string;
  rate_type?: string;
  loan_type_normalized?: string;
  debt_notes?: string;
  
  // Equity-specific fields
  target_return?: number;
  minimum_internal_rate_of_return?: number;
  min_hold_period_years?: number;
  max_hold_period_years?: number;
  minimum_yield_on_cost?: number;
  minimum_equity_multiple?: number;
  ownership_requirement?: string;
  equity_program_overview?: string;
  target_cash_on_cash_min?: number;
  attachment_point?: number;
  max_leverage_tolerance?: number;
  typical_closing_timeline_days?: number;
  proof_of_funds_requirement?: boolean;
  equity_occupancy_requirements?: string;
  equity_notes?: string;
  yield_on_cost?: number;
  target_return_irr_on_equity?: number;
  equity_multiple?: number;
  position_specific_irr?: number;
  position_specific_equity_multiple?: number;
}

interface InvestmentCriteriaSliderV2Props {
  entityType: 'contact' | 'company'
  entityId: string | number
  entityEmail?: string
  entityName?: string
}

export default function InvestmentCriteriaSliderV2({ 
  entityType, 
  entityId, 
  entityEmail, 
  entityName 
}: InvestmentCriteriaSliderV2Props) {
  const [criteriaList, setCriteriaList] = useState<InvestmentCriteriaData[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [currentIndex, setCurrentIndex] = useState(0)
  const [showAddForm, setShowAddForm] = useState(false)
  const [showBatchForm, setShowBatchForm] = useState(false)
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)
  const [criteriaToDelete, setCriteriaToDelete] = useState<number | null>(null)
  const [isSaving, setIsSaving] = useState(false)

  useEffect(() => {
  const fetchInvestmentCriteria = async () => {
      try {
    setLoading(true)
        let response: Response

        // Choose the appropriate API endpoint based on entity type
        if (entityType === 'contact') {
          response = await fetch(`/api/investment-criteria/by-contact/${entityId}`)
          
          // Fallback to search by email if that fails
          if (!response.ok && entityEmail) {
            response = await fetch(`/api/investment-criteria?email=${encodeURIComponent(entityEmail)}`)
          }
        } else {
          response = await fetch(`/api/investment-criteria/by-company/${entityId}`)
          
          // Fallback to search by company name if that fails
          if (!response.ok && entityName) {
            response = await fetch(`/api/investment-criteria?entityName=${encodeURIComponent(entityName)}`)
          }
        }
      
      if (!response.ok) {
          throw new Error('Failed to fetch investment criteria')
        }

        const data = await response.json()
        const criteria = Array.isArray(data) ? data : (data.criteria || [])
        
        // Debug logging
        console.log('V2 InvestmentCriteriaSlider - Fetched data:', {
          entityType,
          entityId,
          criteriaCount: criteria.length,
          firstCriteria: criteria[0],
          allCriteria: criteria
        })
        
        setCriteriaList(criteria)
        
        // Reset to first item if we have criteria
        if (criteria.length > 0) {
          setCurrentIndex(0)
      }
    } catch (err) {
      console.error('Error fetching investment criteria:', err)
        setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setLoading(false)
    }
  }

    fetchInvestmentCriteria()
  }, [entityType, entityId, entityEmail, entityName])

  const goToPrevious = () => {
    setCurrentIndex((prevIndex) => 
      prevIndex > 0 ? prevIndex - 1 : criteriaList.length - 1
    )
  }

  const goToNext = () => {
    setCurrentIndex((prevIndex) => 
      prevIndex < criteriaList.length - 1 ? prevIndex + 1 : 0
    )
  }

  const getEntityIcon = (entityType: string) => {
    if (entityType.startsWith('Company')) {
      return <Building2 className="h-4 w-4 text-blue-600" />
    } else if (entityType === 'Contact') {
      return <User className="h-4 w-4 text-emerald-600" />
    } else if (entityType === 'Deal') {
      return <Briefcase className="h-4 w-4 text-purple-600" />
    } else {
      return <FileText className="h-4 w-4 text-slate-600" />
    }
  }

  // Generate IC label (capital position + loan type + deal size)
  const generateICLabel = (criteria: any) => {
    const parts: string[] = []
    
    // Add capital position (from new table structure)
    if (criteria.capital_position) {
      parts.push(criteria.capital_position)
    }
    
    // Add deal size range
    if (criteria.minimum_deal_size || criteria.maximum_deal_size) {
      const formatCurrency = (value: number) => {
        if (value >= 1000000000) {
          return `$${(value / 1000000000).toFixed(1)}B`
        }
        if (value >= 1000000) {
          return `$${(value / 1000000).toFixed(1)}M`
        }
        if (value >= 1000) {
          return `$${(value / 1000).toFixed(0)}K`
        }
        return `$${value.toLocaleString()}`
      }
      
      let dealSizeStr = ''
      if (criteria.minimum_deal_size && criteria.maximum_deal_size) {
        dealSizeStr = `${formatCurrency(criteria.minimum_deal_size)} - ${formatCurrency(criteria.maximum_deal_size)}`
      } else if (criteria.minimum_deal_size) {
        dealSizeStr = `${formatCurrency(criteria.minimum_deal_size)}+`
      } else if (criteria.maximum_deal_size) {
        dealSizeStr = `up to ${formatCurrency(criteria.maximum_deal_size)}`
      }
      
      if (dealSizeStr) {
        parts.push(dealSizeStr)
      }
    }
    
    return parts.join(' | ') || 'Investment Criteria'
  }

  const handleAddNew = () => {
    setShowAddForm(true)
  }

  const handleBatchAdd = () => {
    setShowBatchForm(true)
  }

  const handleCancelAdd = () => {
    setShowAddForm(false)
  }

  const handleCancelBatch = () => {
    setShowBatchForm(false)
  }

  const handleBatchSave = async (criteriaList: any[]) => {
    try {
      setIsSaving(true)
      
      // Save all criteria in sequence
      for (const formData of criteriaList) {
        const response = await fetch(`/api/investment-criteria-entity/${entityType}/${entityId}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(formData),
        })
        
        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}))
          throw new Error(errorData.error || `Failed to create investment criteria: ${response.statusText}`)
        }
      }
      
      // Refresh the criteria list when new data is added
      const refreshResponse = await fetch(`/api/investment-criteria/by-${entityType}/${entityId}`)
      if (refreshResponse.ok) {
        const data = await refreshResponse.json()
        const criteria = Array.isArray(data) ? data : (data.criteria || [])
        setCriteriaList(criteria)
        setCurrentIndex(criteria.length - 1) // Set to the last added criteria
      }
      
      setShowBatchForm(false)
    } catch (error) {
      console.error('Error saving batch criteria:', error)
      throw error // Let the form handle the error display
    } finally {
      setIsSaving(false)
    }
  }



  const handleDelete = async (criteriaId: number) => {
    // Show custom confirmation dialog
    setCriteriaToDelete(criteriaId)
    setShowDeleteConfirm(true)
  }

  const confirmDelete = async () => {
    if (!criteriaToDelete) return
    
    try {
      const response = await fetch(`/api/investment-criteria-entity/${entityType}/${entityId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          criteria_id: criteriaToDelete
        }),
      })
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || `Failed to delete investment criteria: ${response.statusText}`)
      }
      
      const result = await response.json()
      
      if (result.success) {
        // Refresh the criteria list
        const refreshData = async () => {
          try {
            const response = await fetch(`/api/investment-criteria/by-${entityType}/${entityId}`)
            if (response.ok) {
              const data = await response.json()
              const criteria = Array.isArray(data) ? data : (data.criteria || [])
              setCriteriaList(criteria)
              // Reset to first item if we deleted the current one
              if (currentIndex >= criteria.length) {
                setCurrentIndex(0)
              }
            }
          } catch (error) {
            console.error('Error refreshing investment criteria:', error)
          }
        }
        refreshData()
      } else {
        throw new Error(result.error || 'Failed to delete investment criteria')
      }
    } catch (error) {
      console.error('Error deleting investment criteria:', error)
      // You might want to show a toast notification here
    } finally {
      setShowDeleteConfirm(false)
      setCriteriaToDelete(null)
    }
  }

  const cancelDelete = () => {
    setShowDeleteConfirm(false)
    setCriteriaToDelete(null)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="flex flex-col items-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
          <p className="text-gray-600">Loading investment criteria...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <Card className="bg-white shadow-sm">
        <CardContent className="pt-6">
          <div className="text-center py-6">
            <AlertCircle className="h-12 w-12 mx-auto text-red-500 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Error Loading Investment Criteria
            </h3>
            <p className="text-red-500 mb-4">{error}</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Show the batch form when adding multiple criteria
  if (showBatchForm) {
    return (
      <div className="space-y-4">
        <BatchInvestmentCriteriaForm
          entityType={entityType}
          entityId={entityId}
          onSave={handleBatchSave}
          onCancel={handleCancelBatch}
          isSaving={isSaving}
        />
      </div>
    )
  }

  // Show the add form when adding new criteria
  if (showAddForm) {
    return (
      <div className="space-y-4">
        <InvestmentCriteriaForm
          onSave={async (formData, hasChangedCentralCriteria) => {
            try {
              setIsSaving(true)
              const response = await fetch(`/api/investment-criteria-entity/${entityType}/${entityId}`, {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData),
              })
              
              if (!response.ok) {
                const errorData = await response.json().catch(() => ({}))
                throw new Error(errorData.error || `Failed to create investment criteria: ${response.statusText}`)
              }
              
              // Refresh the criteria list when new data is added
              const refreshResponse = await fetch(`/api/investment-criteria/by-${entityType}/${entityId}`)
              if (refreshResponse.ok) {
                const data = await refreshResponse.json()
                const criteria = Array.isArray(data) ? data : (data.criteria || [])
                setCriteriaList(criteria)
                setCurrentIndex(criteria.length - 1) // Set to the newly added criteria
              }
              
              setShowAddForm(false)
            } catch (error) {
              console.error('Error adding investment criteria:', error)
              throw error // Let the form handle the error display
            } finally {
              setIsSaving(false)
            }
          }}
          onCancel={handleCancelAdd}
          isEditing={false}
          isSaving={isSaving}
          showCompanySelection={false}
          isEditingCopiedIC={false}
          entityType={entityType}
          entityId={entityId}
        />
      </div>
    )
  }

  if (criteriaList.length === 0) {
    return (
      <Card className="bg-white shadow-sm">
        <CardContent className="pt-6">
          <div className="text-center py-8">
            <Target className="h-12 w-12 mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-700 mb-2">
              No Investment Criteria Found
            </h3>
            <p className="text-gray-500 max-w-md mx-auto mb-6">
              No investment criteria records have been found for this {entityType}.
            </p>
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <Button onClick={handleAddNew} className="flex items-center">
                <Plus className="h-4 w-4 mr-2" />
                Add Single Criteria
              </Button>
              <Button onClick={handleBatchAdd} variant="outline" className="flex items-center">
                <Target className="h-4 w-4 mr-2" />
                Add Multiple Criteria
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  const currentCriteria = criteriaList[currentIndex]

  return (
    <div className="space-y-4">
      {/* Navigation Header */}
        <Card className="bg-white shadow-sm">
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                {getEntityIcon(currentCriteria.entity_type)}
                <div>
                  <CardTitle className="text-lg font-medium">
                    {generateICLabel(currentCriteria)}
                  </CardTitle>
                  <p className="text-sm text-slate-500">
                  Showing {currentIndex + 1} of {criteriaList.length} criteria records
                  </p>
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleAddNew}
                    className="flex items-center"
                  >
                    <Plus className="h-4 w-4 mr-1" />
                    Add Single
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleBatchAdd}
                    className="flex items-center"
                  >
                    <Target className="h-4 w-4 mr-1" />
                    Add Multiple
                  </Button>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToPrevious}
                disabled={criteriaList.length <= 1}
                >
                  <ChevronLeft className="h-4 w-4 mr-1" />
                  Previous
                </Button>

                <div className="flex items-center gap-1 px-3">
                {criteriaList.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => setCurrentIndex(index)}
                      className={`w-2 h-2 rounded-full transition-colors ${
                        index === currentIndex
                          ? 'bg-blue-600'
                          : 'bg-gray-300 hover:bg-gray-400'
                      }`}
                    title={`Go to criteria ${index + 1}`}
                    />
                  ))}
                </div>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToNext}
                disabled={criteriaList.length <= 1}
                >
                  Next
                  <ChevronRight className="h-4 w-4 ml-1" />
                </Button>
              </div>
            </div>
          </CardHeader>
          
          <CardContent className="pt-0">
            <div className="flex flex-wrap gap-2">
            {criteriaList.map((criteria, index) => (
                <button
                  key={criteria.investment_criteria_id}
                  onClick={() => setCurrentIndex(index)}
                  className={`px-3 py-1 rounded-lg text-sm transition-colors ${
                    index === currentIndex
                      ? 'bg-blue-100 text-blue-800 border border-blue-200'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {generateICLabel(criteria)}
                </button>
              ))}
            </div>
          </CardContent>
        </Card>

      {/* Full Detail View */}
        <div className="bg-white rounded-lg">
                <InvestmentCriteriaDetailViewV2
          entityType={entityType}
          entityId={entityId}
          initialCriteria={currentCriteria}
          onDataUpdate={async () => {
            // Refresh the criteria list when data is updated
            try {
              const response = await fetch(`/api/investment-criteria/by-${entityType}/${entityId}`)
              if (response.ok) {
                const data = await response.json()
                const criteria = Array.isArray(data) ? data : (data.criteria || [])
                setCriteriaList(criteria)
                // Keep the same index if possible, otherwise reset to 0
                if (currentIndex >= criteria.length) {
                  setCurrentIndex(0)
                }
              }
            } catch (error) {
              console.error('Error refreshing investment criteria:', error)
            }
          }}
          onDelete={handleDelete}
        />
        </div>

      {/* Delete Confirmation Dialog */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex items-center mb-4">
              <AlertCircle className="h-6 w-6 text-red-500 mr-3" />
              <h3 className="text-lg font-semibold text-gray-900">Delete Investment Criteria</h3>
            </div>
            <p className="text-gray-600 mb-6">
              Are you sure you want to delete this investment criteria? This action cannot be undone and will permanently remove all associated data including debt and equity information.
            </p>
            <div className="flex justify-end space-x-3">
              <Button variant="outline" onClick={cancelDelete}>
                Cancel
              </Button>
              <Button variant="destructive" onClick={confirmDelete}>
                Delete
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
