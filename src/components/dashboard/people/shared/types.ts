// Shared types for contact components
// Based on the actual database schema from contacts table

export interface Contact {
  // Core identifiers
  contact_id?: number;
  company_id?: number;
  
  // Personal Information
  first_name?: string;
  last_name?: string;
  full_name?: string;
  title?: string; // job title
  job_tier?: string;
  headline?: string;
  seniority?: string;
  executive_summary?: string;
  career_timeline?: any[]; // JSON array
  
  // Contact Information  
  email?: string;
  additional_email?: string;
  personal_email?: string;
  email_status?: string;
  phone_number?: string;
  phone_number_secondary?: string;
  
  // Social Media
  linkedin_url?: string;
  twitter?: string;
  facebook?: string;
  instagram?: string;
  youtube?: string;
  
  // Education
  education_college?: string;
  education_college_year_graduated?: string;
  education_high_school?: string;
  education_high_school_year_graduated?: string;
  
  // Personal Details
  age?: string;
  honorable_achievements?: string[]; // JSON array
  hobbies?: string[]; // JSON array
  
  // Contact Location
  contact_address?: string;
  contact_city?: string;
  contact_state?: string;
  contact_zip_code?: string;
  contact_country?: string;
  region?: string;
  
  // Professional Details
  contact_type?: string;
  relationship_owner?: string;
  role_in_decision_making?: string;
  last_contact_date?: string;
  source_of_introduction?: string;
  accredited_investor_status?: boolean;
  kyc_status?: string;
  
  // Company Information
  company_name?: string;
  
  // Processing and Status Fields
  processing_state?: string;
  email_verification_status?: string;
  email_verification_date?: string;
  email_verification_error?: string;
  email_generation_status?: string;
  email_generation_date?: string;
  email_generation_error?: string;
  email_sending_status?: string;
  email_sending_date?: string;
  email_sending_error?: string;
  contact_enrichment_v2_status?: string;
  contact_enrichment_v2_date?: string;
  contact_enrichment_v2_error?: string;
  contact_investment_criteria_status?: string;
  contact_investment_criteria_date?: string;
  contact_investment_criteria_error?: string;
  last_processed_stage?: string;
  last_processed_at?: string;
  processing_error_count?: number;
  processing_attempts?: any;
  
  // Campaign and Email Management
  smartlead_lead_id?: string;
  smartlead_status?: string;
  last_email_sent_at?: string;
  email_batch_identifier?: string;
  email_generated?: boolean;
  email_sent_error?: string;
  email_sent_date?: string;
  
  // Conflicts
  conflicts?: any;
  conflict_status?: string;
  conflict_created_at?: string;
  conflict_resolved_at?: string;
  conflict_source?: string;
  
  // Metadata
  source?: string;
  notes?: string;
  extra_attrs?: any; // JSONB
  created_at?: string;
  updated_at?: string;
  email_validated_date?: string;
}


export interface CompanySuggestion {
  company_id?: number;
  company_name: string;
  company_website?: string;
  industry?: string;
  company_address?: string;
  company_city?: string;
  company_state?: string;
  company_country?: string;
  company_zip?: string;
  company_phone?: string;
  company_email?: string;
  employee_count?: string;
  revenue_range?: string;
  investment_criteria_count?: number;
  // Extracted data for enhanced auto-fill
  extracted_data?: {
    companytype?: string;
    businessmodel?: string;
    fundsize?: string;
    aum?: string;
    headquarters?: string;
    foundedyear?: number;
    numberofemployees?: string;
    investmentfocus?: string[];
    geographicfocus?: string[];
    dealsize?: string;
    minimumdealsize?: string;
    maximumdealsize?: string;
    investment_criteria_property_types?: string[];
    investment_criteria_asset_types?: string[];
    investment_criteria_loan_types?: string[];
    investment_criteria_property_subcategories?: string[];
    riskprofile?: string;
    targetmarkets?: string[];
    strategies?: string[];
    propertytypes?: string[];
    assetclasses?: string[];
    valuecreation?: string[];
    holdperiod?: string;
    targetreturn?: string;
    approach?: string;
  };
}

export interface ExistingContact extends Contact {
  match_type?: 'email' | 'linkedin' | 'name_company' | 'additional_email';
  match_field?: string;
  match_value?: string;
}

export interface ContactFormData {
  // Personal Information
  first_name: string;
  last_name: string;
  job_title: string;
  job_tier: string;
  full_name?: string;

  // Contact Information
  email: string;
  personal_email: string;
  additional_email?: string;
  phone_number: string;
  phone_number_secondary: string;

  // Social Media
  linkedin_url: string;
  twitter?: string;
  facebook?: string;
  instagram?: string;
  youtube?: string;

  // Contact Location
  contact_address: string;
  contact_city: string;
  contact_state: string;
  contact_zip_code: string;
  contact_country: string;
  last_contact_date: string;
  last_contact_at?: string;
  source_of_introduction: string;

  // Professional Information
  executive_summary?: string;
  career_timeline?: string[];
  contact_type?: string;
  relationship_owner?: string;
  role_in_decision_making?: string;
  accredited_investor_status?: boolean;
  kyc_status?: string;

  // Education
  education_college?: string;
  education_college_year_graduated?: string;
  education_high_school?: string;
  education_high_school_year_graduated?: string;

  // Personal Details
  honorable_achievements?: string[];
  hobbies?: string[];
  age?: string;

  // Company Information (for selection, not stored in contact table)
  company_name: string;
  company_website?: string;
  industry?: string;
  company_address?: string;
  company_city?: string;
  company_state?: string;
  company_country?: string;
  company_zip?: string;
}

export interface ValidationState {
  isValidating: boolean;
  isDuplicate: boolean;
  duplicateMessage?: string;
}

export interface FieldValidationStates {
  personal_email: ValidationState;
  linkedin_url: ValidationState;
  full_name: ValidationState;
}

// Simplified field groups for the new contact form structure
export interface ContactFieldGroups {
  personalInfo: {
    title: string;
    icon: string;
    fields: Array<{
      name: keyof ContactFormData;
      label: string;
      type: 'text' | 'email' | 'tel' | 'textarea' | 'select' | 'checkbox' | 'date' | 'datetime-local' | 'array';
      required?: boolean;
      placeholder?: string;
      options?: string[];
      validation?: string;
      data_origin?: string;
    }>;
  };
  contactInfo: {
    title: string;
    icon: string;
    fields: Array<{
      name: keyof ContactFormData;
      label: string;
      type: 'text' | 'email' | 'tel' | 'textarea' | 'select' | 'checkbox' | 'date' | 'datetime-local' | 'array';
      required?: boolean;
      placeholder?: string;
      options?: string[];
      validation?: string;
      data_origin?: string;
    }>;
  };
  socialMedia: {
    title: string;
    icon: string;
    fields: Array<{
      name: keyof ContactFormData;
      label: string;
      type: 'text' | 'email' | 'tel' | 'textarea' | 'select' | 'checkbox' | 'date' | 'datetime-local' | 'array';
      required?: boolean;
      placeholder?: string;
      options?: string[];
      validation?: string;
      data_origin?: string;
    }>;
  };
  education: {
    title: string;
    icon: string;
    fields: Array<{
      name: keyof ContactFormData;
      label: string;
      type: 'text' | 'email' | 'tel' | 'textarea' | 'select' | 'checkbox' | 'date' | 'datetime-local' | 'array';
      required?: boolean;
      placeholder?: string;
      options?: string[];
      validation?: string;
      data_origin?: string;
    }>;
  };
  personalDetails: {
    title: string;
    icon: string;
    fields: Array<{
      name: keyof ContactFormData;
      label: string;
      type: 'text' | 'email' | 'tel' | 'textarea' | 'select' | 'checkbox' | 'date' | 'datetime-local' | 'array';
      required?: boolean;
      placeholder?: string;
      options?: string[];
      validation?: string;
      data_origin?: string;
    }>;
  };
}

// Define the simplified contact field groups for AddContact - excludes enrichment fields
// These fields should be populated by ContactEnrichmentProcessorV2, not manually entered
export const CONTACT_FIELD_GROUPS: ContactFieldGroups = {
  personalInfo: {
    title: "Personal Information",
    icon: "Users",
    fields: [
      {
        name: "first_name",
        label: "First Name",
        type: "text",
        required: true,
        placeholder: "Enter first name",
        data_origin: "user_input"
      },
      {
        name: "last_name",
        label: "Last Name",
        type: "text",
        required: true,
        placeholder: "Enter last name",
        data_origin: "user_input"
      },
      {
        name: "job_title",
        label: "Job Title",
        type: "text",
        placeholder: "Enter job title",
        data_origin: "user_input"
      },
      {
        name: "job_tier",
        label: "Job Tier",
        type: "select",
        placeholder: "Select job tier",
        data_origin: "user_input"
      }
    ]
  },
  contactInfo: {
    title: "Contact Information",
    icon: "Phone",
    fields: [
      {
        name: "email",
        label: "Email",
        type: "email",
        placeholder: "Enter email",
        validation: "max 255 chars",
        data_origin: "user_input"
      },
      {
        name: "personal_email",
        label: "Personal Email",
        type: "email",
        placeholder: "Enter personal email",
        validation: "max 255 chars",
        data_origin: "user_input"
      },
      {
        name: "phone_number",
        label: "Contact Phone",
        type: "tel",
        placeholder: "Enter contact phone",
        validation: "max 100 chars",
        data_origin: "user_input"
      },
      {
        name: "phone_number_secondary",
        label: "Secondary Phone",
        type: "tel",
        placeholder: "Enter secondary phone",
        validation: "max 100 chars",
        data_origin: "user_input"
      },
      {
        name: "contact_address",
        label: "Address",
        type: "text",
        placeholder: "Enter address",
        data_origin: "user_input"
      },
      {
        name: "contact_city",
        label: "City",
        type: "text",
        placeholder: "Enter city",
        data_origin: "user_input"
      },
      {
        name: "contact_state",
        label: "State",
        type: "text",
        placeholder: "Enter state",
        data_origin: "user_input"
      },
      {
        name: "contact_zip_code",
        label: "Zip Code",
        type: "text",
        placeholder: "Enter zip code",
        data_origin: "user_input"
      },
      {
        name: "contact_country",
        label: "Country",
        type: "text",
        placeholder: "Enter country",
        data_origin: "user_input"
      },
      {
        name: "last_contact_date",
        label: "Last Contact Date",
        type: "date",
        placeholder: "Select last contact date",
        data_origin: "user_input"
      },
      {
        name: "source_of_introduction",
        label: "Source of Introduction",
        type: "text",
        placeholder: "Enter source of introduction",
        data_origin: "user_input"
      }
    ]
  },
  socialMedia: {
    title: "Social Media",
    icon: "Globe",
    fields: [
      {
        name: "linkedin_url",
        label: "LinkedIn URL",
        type: "text",
        placeholder: "Enter LinkedIn URL",
        data_origin: "user_input"
      }
    ]
  },
  education: {
    title: "Education",
    icon: "GraduationCap",
    fields: [
      {
        name: "education_college",
        label: "College/University",
        type: "text",
        placeholder: "Enter college or university",
        data_origin: "user_input"
      },
      {
        name: "education_college_year_graduated",
        label: "Graduation Year",
        type: "text",
        placeholder: "Enter graduation year",
        data_origin: "user_input"
      },
      {
        name: "education_high_school",
        label: "High School",
        type: "text",
        placeholder: "Enter high school",
        data_origin: "user_input"
      },
      {
        name: "education_high_school_year_graduated",
        label: "High School Graduation Year",
        type: "text",
        placeholder: "Enter high school graduation year",
        data_origin: "user_input"
      }
    ]
  },
  personalDetails: {
    title: "Personal Details",
    icon: "User",
    fields: [
      {
        name: "age",
        label: "Age",
        type: "text",
        placeholder: "Enter age",
        data_origin: "user_input"
      },
      {
        name: "honorable_achievements",
        label: "Honorable Achievements",
        type: "array",
        placeholder: "Enter honorable achievements",
        data_origin: "user_input"
      },
      {
        name: "hobbies",
        label: "Hobbies",
        type: "array",
        placeholder: "Enter hobbies",
        data_origin: "user_input"
      }
    ]
  }
};

// Form validation rules
export interface ContactValidationRules {
  [key: string]: {
    required?: boolean;
    minLength?: number;
    maxLength?: number;
    pattern?: RegExp;
    validator?: (value: any) => boolean;
    errorMessage?: string;
  };
}

// Search result for contacts
export interface ContactSearchResult {
  contacts: Contact[];
  total: number;
  hasMore: boolean;
}

// Contact search filters
export interface ContactSearchFilters {
  search?: string;
  email?: string;
  linkedin_url?: string;
  company_id?: number;
  contact_type?: string;
  limit?: number;
  offset?: number;
}

// Unified contact data type for the unified filters API response
export interface UnifiedContactData extends Contact {
  // Additional fields from the API response
  contact_enrichment_v2_status?: string;
  contact_enrichment_v2_error?: string;
  contact_investment_criteria_status?: string;
  contact_investment_criteria_error?: string;
  criteria_id?: string | number;
} 