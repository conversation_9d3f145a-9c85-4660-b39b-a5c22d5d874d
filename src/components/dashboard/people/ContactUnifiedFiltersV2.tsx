"use client";

import React, { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { ReactMultiSelect } from '@/components/ui/react-multi-select';
import NestedMappingSelector from '../investment-criteria/NestedMappingSelector';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { 
  X, Filter, RotateCcw, Search, SlidersHorizontal, ChevronRight, Sparkles,
  Building, DollarSign, MapPin, Settings, BarChart3, TrendingUp, Building2,
  Target, Briefcase, Calculator, Calendar, Users, Globe, ArrowUp, ArrowDown, 
  Clock, Banknote, Percent, Timer, LineChart, Activity, PieChart, User, UserCheck,
  Mail, CheckCircle, AlertCircle, MessageSquare, Send, Loader2, Brain, Eye,
  Database, Table, Shield, Minus, Plus, Eye as EyeIcon, EyeOff, GraduationCap,
  Trophy, Heart, Phone, TwitterIcon, Facebook, Instagram, Youtube, MapPinIcon,
  FileText, Briefcase as BriefcaseIcon, Contact, UserCog, Scale, Layers,
  Factory, Star, Award, MessageCircle, TrendingDown, Network, HandHeart,
  Zap, TreePine, Gavel, Landmark
} from 'lucide-react';
import type { ContactUnifiedFiltersV2 } from "../../../types/unified-filters-v2";

interface MappingsData {
  [type: string]: {
    parents: string[]
    children: string[]
    hierarchical: {
      [parent: string]: string[]
    }
  }
}

interface SourceCount {
  source: string;
  count: string;
}

interface JobTierCount {
  job_tier: string;
  count: number;
}

interface ContactUnifiedFiltersV2Props {
  filters: ContactUnifiedFiltersV2;
  mappings?: MappingsData;
  onFiltersChange: (filters: ContactUnifiedFiltersV2) => void;
  onClearFilters: () => void;
  isLoading?: boolean;
}

export default function ContactUnifiedFiltersV2({
  filters,
  mappings,
  onFiltersChange,
  onClearFilters,
  isLoading = false
}: ContactUnifiedFiltersV2Props) {
  const { data: session } = useSession();
  const userRole = session?.user?.role || 'guest';
  
  const [isFilterPanelOpen, setIsFilterPanelOpen] = useState(false);
  const [localFilters, setLocalFilters] = useState<ContactUnifiedFiltersV2>(filters);
  const [pendingFilters, setPendingFilters] = useState<ContactUnifiedFiltersV2>(filters);
  const [localRangeInputs, setLocalRangeInputs] = useState<{[key: string]: string}>({});
  
  // Filter options state
  const [sources, setSources] = useState<Array<{value: string, label: string, count?: string | number}>>([]);
  const [emailStatus, setEmailStatus] = useState<Array<{value: string, label: string, count?: string | number}>>([]);
  const [jobTiers, setJobTiers] = useState<Array<{value: string, label: string, count?: number}>>([]);
  const [loadingSources, setLoadingSources] = useState(false);
  const [loadingEmailStatus, setLoadingEmailStatus] = useState(false);
  const [loadingJobTiers, setLoadingJobTiers] = useState(false);

  // V2 Enrichment Filter Options
  const [enrichmentV2Options, setEnrichmentV2Options] = useState<{
    contactTypes: Array<{value: string, label: string}>,
    relationshipOwners: Array<{value: string, label: string}>,
    rolesInDecisionMaking: Array<{value: string, label: string}>,
    sourcesOfIntroduction: Array<{value: string, label: string}>,
    educationColleges: Array<{value: string, label: string}>,
    educationCollegeYearGraduated: Array<{value: string, label: string}>,
    educationHighSchools: Array<{value: string, label: string}>,
    ageRanges: Array<{value: string, label: string}>,
  }>({
    contactTypes: [],
    relationshipOwners: [],
    rolesInDecisionMaking: [],
    sourcesOfIntroduction: [],
    educationColleges: [],
    educationCollegeYearGraduated: [],
    educationHighSchools: [],
    ageRanges: [],
  });
  const [loadingEnrichmentV2Options, setLoadingEnrichmentV2Options] = useState(false);

      // Company processing status options are now static (not fetched from API)
    // These are kept as predefined options since they're all the same status options

  // Investment Criteria Filter Options
  const [investmentCriteriaOptions, setInvestmentCriteriaOptions] = useState<{
    capitalPositions: Array<{value: string, label: string}>,
    propertyTypes: Array<{value: string, label: string}>,
    propertySubcategories: Array<{value: string, label: string}>,
    strategies: Array<{value: string, label: string}>,
    loanTypes: Array<{value: string, label: string}>,
    structuredLoanTranches: Array<{value: string, label: string}>,
    loanPrograms: Array<{value: string, label: string}>,
    recourseLoans: Array<{value: string, label: string}>,
    countries: Array<{value: string, label: string}>,
    regions: Array<{value: string, label: string}>,
    states: Array<{value: string, label: string}>,
    cities: Array<{value: string, label: string}>,
    decisionMakingProcesses: Array<{value: string, label: string}>,
    // Debt specific fields
    eligibleBorrowers: Array<{value: string, label: string}>,
    lienPositions: Array<{value: string, label: string}>,
    rateLocks: Array<{value: string, label: string}>,
    rateTypes: Array<{value: string, label: string}>,
    loanTypeNormalized: Array<{value: string, label: string}>,
    amortizations: Array<{value: string, label: string}>,
    loanMinDebtYield: Array<{value: string, label: string}>,
    futureFacilities: Array<{value: string, label: string}>,
    occupancyRequirements: Array<{value: string, label: string}>,
    prepayments: Array<{value: string, label: string}>,
    yieldMaintenances: Array<{value: string, label: string}>,
    // Equity specific fields
    ownershipRequirements: Array<{value: string, label: string}>,
  }>({
    capitalPositions: [],
    propertyTypes: [],
    propertySubcategories: [],
    strategies: [],
    loanTypes: [],
    structuredLoanTranches: [],
    loanPrograms: [],
    recourseLoans: [],
    countries: [],
    regions: [],
    states: [],
    cities: [],
    decisionMakingProcesses: [],
    eligibleBorrowers: [],
    lienPositions: [],
    rateLocks: [],
    rateTypes: [],
    loanTypeNormalized: [],
    amortizations: [],
    loanMinDebtYield: [],
    futureFacilities: [],
    occupancyRequirements: [],
    prepayments: [],
    yieldMaintenances: [],
    ownershipRequirements: [],
  });
  const [loadingInvestmentCriteriaOptions, setLoadingInvestmentCriteriaOptions] = useState(false);

  // Raw mapping data for NestedMappingSelector components
  const [mappingData, setMappingData] = useState<{
    propertyTypeRaw?: any;
    regionsRaw?: any;
  }>({
    propertyTypeRaw: null,
    regionsRaw: null
  });

  // Processing status options with null handling
  const processingStatusOptions = [
    { value: 'not_started', label: 'Not Started', icon: Minus, color: 'from-gray-50 to-slate-50' },
    { value: 'pending', label: 'Pending', icon: Clock, color: 'from-yellow-50 to-amber-50' },
    { value: 'running', label: 'Running', icon: Loader2, color: 'from-blue-50 to-indigo-50' },
    { value: 'completed', label: 'Completed', icon: CheckCircle, color: 'from-green-50 to-emerald-50' },
    { value: 'failed', label: 'Failed', icon: AlertCircle, color: 'from-red-50 to-pink-50' },
    { value: 'error', label: 'Error', icon: AlertCircle, color: 'from-red-50 to-pink-50' }
  ];

  // Smartlead sync status options
  const smartleadSyncStatusOptions = [
    { value: 'null', label: 'Not Synced (NULL)', icon: Minus, color: 'from-gray-50 to-slate-50' },
    { value: 'pending', label: 'Pending', icon: Clock, color: 'from-yellow-50 to-amber-50' },
    { value: 'running', label: 'Running', icon: Loader2, color: 'from-blue-50 to-indigo-50' },
    { value: 'completed', label: 'Completed', icon: CheckCircle, color: 'from-green-50 to-emerald-50' },
    { value: 'failed', label: 'Failed', icon: AlertCircle, color: 'from-red-50 to-pink-50' },
    { value: 'error', label: 'Error', icon: AlertCircle, color: 'from-red-50 to-pink-50' },
    { value: 'ACTIVE', label: 'Active (Synced)', icon: CheckCircle, color: 'from-green-50 to-emerald-50' },
    { value: 'ADDED', label: 'Added', icon: Plus, color: 'from-blue-50 to-indigo-50' }
  ];

      // Individual NOT filter modes for each specific filter
    const [filterNotModes, setFilterNotModes] = useState<{[key: string]: boolean}>({
      source: false,
      emailStatus: false,
      emailVerificationStatus: false,
      contactEnrichmentV2Status: false,
      contactInvestmentCriteriaStatus: false,
      emailGenerationStatus: false,
      emailSendingStatus: false,
      smartleadSyncStatus: false,
      propertyTypes: false,
      propertySubcategories: false,
      strategies: false,
      capitalPosition: false,
      loanTypes: false,
      structuredLoanTranche: false,
      loanProgram: false,
      recourseLoan: false,
      contactType: false,
      companyWebsiteScrapingStatus: false,
      companyOverviewV2Status: false,
    });

  // Track which table sections are expanded
  const [expandedSections, setExpandedSections] = useState<{[key: string]: boolean}>({
    contacts: true,
    contact_enrichment_v2: false,
    company_processors: false,
    investment_criteria: false,
    gmail_outreach: false
  });

  // Search functionality state
  const [searchTerm, setSearchTerm] = useState('');
  const [searchCriteria, setSearchCriteria] = useState<'fullNameSearch' | 'emailSearch' | 'companyNameSearch'>('fullNameSearch');

  // Fetch filter options on component mount
  useEffect(() => {
    fetchAllFilterOptions();
  }, []);

  // Initialize search term from current filters
  useEffect(() => {
    if (filters.fullNameSearch) {
      setSearchTerm(filters.fullNameSearch);
      setSearchCriteria('fullNameSearch');
    } else if (filters.emailSearch) {
      setSearchTerm(filters.emailSearch);
      setSearchCriteria('emailSearch');
    } else if (filters.companyNameSearch) {
      setSearchTerm(filters.companyNameSearch);
      setSearchCriteria('companyNameSearch');
    }
  }, [filters.fullNameSearch, filters.emailSearch, filters.companyNameSearch]);

  // Handle search functionality - trigger search when search term or criteria changes
  useEffect(() => {
    const timer = setTimeout(() => {
      // Clear other search fields when switching criteria
      const updatedFilters = {
        ...pendingFilters,
        fullNameSearch: searchCriteria === 'fullNameSearch' ? (searchTerm.trim() || undefined) : undefined,
        emailSearch: searchCriteria === 'emailSearch' ? (searchTerm.trim() || undefined) : undefined,
        companyNameSearch: searchCriteria === 'companyNameSearch' ? (searchTerm.trim() || undefined) : undefined,
        page: 1 // Reset to first page when searching
      };
      
      setPendingFilters(updatedFilters);
      setLocalFilters(updatedFilters);
      onFiltersChange(updatedFilters);
    }, 300); // 300ms debounce

    return () => clearTimeout(timer);
  }, [searchTerm, searchCriteria]);

  const fetchAllFilterOptions = async () => {
    await Promise.all([
      fetchCoreContactsOptions(),
      fetchEnrichmentV2Options(),
      fetchInvestmentCriteriaOptions(),
    ]);
  };

  async function fetchCoreContactsOptions() {
    setLoadingSources(true);
    setLoadingEmailStatus(true);
    setLoadingJobTiers(true);
    try {
      const response = await fetch('/api/contacts/filter-options-v2?section=core_contacts');
      if (response.ok) {
        const data = await response.json();
        setSources(data.sources || []);
        setEmailStatus(data.emailStatus || []);
        setJobTiers(data.jobTiers || []);
      }
    } catch (error) {
      console.error('Error fetching core contact options:', error);
    } finally {
      setLoadingSources(false);
      setLoadingEmailStatus(false);
      setLoadingJobTiers(false);
    }
  }

  async function fetchEnrichmentV2Options() {
    setLoadingEnrichmentV2Options(true);
    try {
      const response = await fetch('/api/contacts/filter-options-v2?section=enrichment_v2');
      if (response.ok) {
        const data = await response.json();
        setEnrichmentV2Options(data);
      }
    } catch (error) {
      console.error('Error fetching enrichment V2 options:', error);
    } finally {
      setLoadingEnrichmentV2Options(false);
    }
  }

  async function fetchInvestmentCriteriaOptions() {
    setLoadingInvestmentCriteriaOptions(true);
    try {
      const [investmentCriteriaRes, propertyTypeRes, regionsRes] = await Promise.all([
        fetch('/api/contacts/filter-options-v2?section=investment_criteria'),
        fetch('/api/mapping-tables/types?type=Property Type'),
        fetch('/api/mapping-tables/types?type=U.S Regions')
      ]);

      if (investmentCriteriaRes.ok) {
        const data = await investmentCriteriaRes.json();
        setInvestmentCriteriaOptions(data);
      }

      // Fetch raw mapping data for NestedMappingSelector
      if (propertyTypeRes.ok) {
        const propertyTypeData = await propertyTypeRes.json();
        if (propertyTypeData.success) {
          setMappingData(prev => ({ ...prev, propertyTypeRaw: propertyTypeData.data }));
        }
      }

      if (regionsRes.ok) {
        const regionsData = await regionsRes.json();
        if (regionsData.success) {
          setMappingData(prev => ({ ...prev, regionsRaw: regionsData.data }));
        }
      }
    } catch (error) {
      console.error('Error fetching investment criteria options:', error);
    } finally {
      setLoadingInvestmentCriteriaOptions(false);
    }
  }

  // Sync local filters with prop changes
  useEffect(() => {
    setLocalFilters(filters);
    setPendingFilters(filters);
  }, [filters]);

  // Update pending filters (not applied yet)
  const updatePendingFilters = (newFilters: Partial<ContactUnifiedFiltersV2>) => {
    const updatedPendingFilters = { ...pendingFilters, ...newFilters, page: 1 };
    setPendingFilters(updatedPendingFilters);
  };

  // Apply filters when user clicks Apply button
  const applyFilters = () => {
    setLocalFilters(pendingFilters);
    onFiltersChange(pendingFilters);
    setIsFilterPanelOpen(false);
  };

  // Handle clear filters
  const handleClearFilters = () => {
    const defaultFilters = {
      page: 1,
      limit: 25,
      sortBy: 'updated_at',
      sortOrder: 'desc' as const
    };
    
    setLocalFilters(defaultFilters);
    setPendingFilters(defaultFilters);
    onFiltersChange(defaultFilters);
  };

  // Handle range input changes with local state
  const updateLocalRangeInput = (key: string, value: string) => {
    setLocalRangeInputs(prev => ({ ...prev, [key]: value }));
  };

  // Apply range filter when user finishes input
  const applyRangeFilter = (key: string, value: string) => {
    const numericValue = value.trim() === '' ? undefined : Number(value);
    updatePendingFilters({ [key]: numericValue } as Partial<ContactUnifiedFiltersV2>);
    
    setLocalRangeInputs(prev => {
      const newState = { ...prev };
      delete newState[key];
      return newState;
    });
  };

  // Get current value for range input
  const getRangeInputValue = (key: string, filterValue?: number) => {
    return localRangeInputs[key] !== undefined ? localRangeInputs[key] : (filterValue || '');
  };

  // Count active filters
  const getActiveFilterCount = () => {
    let count = 0;
    
    // Search filters
    if (pendingFilters.fullNameSearch) count++;
    if (pendingFilters.emailSearch) count++;
    if (pendingFilters.companyNameSearch) count++;
    
    // Basic filters
    if (pendingFilters.sortBy && pendingFilters.sortBy !== 'updated_at') count++;
    if (pendingFilters.sortOrder && pendingFilters.sortOrder !== 'desc') count++;
    
    // Core contact filters
    if (pendingFilters.source?.length) count++;
    if (pendingFilters.emailStatus?.length) count++;
    if (pendingFilters.jobTier?.length) count++;
    if (pendingFilters.contactCountries?.length) count++;
    if (pendingFilters.contactStates?.length) count++;
    if (pendingFilters.contactCities?.length) count++;
    
    // Processing status filters
    if (pendingFilters.emailVerificationStatus?.length) count++;
    if (pendingFilters.contactEnrichmentV2Status?.length) count++;
    if (pendingFilters.contactInvestmentCriteriaStatus?.length) count++;
    if (pendingFilters.emailGenerationStatus?.length) count++;
    if (pendingFilters.emailSendingStatus?.length) count++;
    if (pendingFilters.smartleadSyncStatus?.length) count++;
    
    // Boolean flags
    if (pendingFilters.extracted !== undefined) count++;
    if (pendingFilters.searched !== undefined) count++;
    if (pendingFilters.emailGenerated !== undefined) count++;
    if (pendingFilters.enriched !== undefined) count++;
    if (pendingFilters.hasSmartleadId !== undefined) count++;
    
    // V2 Enrichment filters
    if (pendingFilters.contactType?.length) count++;
    if (pendingFilters.relationshipOwner?.length) count++;
    if (pendingFilters.roleInDecisionMaking?.length) count++;
    if (pendingFilters.sourceOfIntroduction?.length) count++;
    if (pendingFilters.accreditedInvestorStatus !== undefined) count++;
    if (pendingFilters.hasExecutiveSummary !== undefined) count++;
    if (pendingFilters.hasCareerTimeline !== undefined) count++;
    if (pendingFilters.hasAdditionalEmail !== undefined) count++;
    if (pendingFilters.hasSecondaryPhone !== undefined) count++;
    
    // Social media filters
    if (pendingFilters.hasTwitter !== undefined) count++;
    if (pendingFilters.hasFacebook !== undefined) count++;
    if (pendingFilters.hasInstagram !== undefined) count++;
    if (pendingFilters.hasYoutube !== undefined) count++;
    
    // Education filters
    if (pendingFilters.educationCollege?.length) count++;
    if (pendingFilters.educationCollegeYearGraduated?.length) count++;
    
    // Personal details
    if (pendingFilters.hasHonorableAchievements !== undefined) count++;
    if (pendingFilters.hasHobbies !== undefined) count++;
    if (pendingFilters.ageRange?.length) count++;
    if (pendingFilters.hasContactAddress !== undefined) count++;
    if (pendingFilters.hasContactZipCode !== undefined) count++;
    
    // Company processor flags
    if (pendingFilters.companyWebsiteScrapingStatus?.length) count++;
    if (pendingFilters.companyOverviewV2Status?.length) count++;
    if (pendingFilters.companyInvestmentCriteriaStatus?.length) count++;
    if (pendingFilters.companyHasInvestmentCriteria !== undefined) count++;
    
    // Investment criteria filters
    if (pendingFilters.capitalPosition?.length) count++;
    if (pendingFilters.propertyTypes?.length) count++;
    if (pendingFilters.propertySubcategories?.length) count++;
    if (pendingFilters.strategies?.length) count++;
    if (pendingFilters.regions?.length) count++;
    if (pendingFilters.states?.length) count++;
    if (pendingFilters.cities?.length) count++;
    if (pendingFilters.countries?.length) count++;
    if (pendingFilters.dealSizeMin !== undefined) count++;
    if (pendingFilters.dealSizeMax !== undefined) count++;
    if (pendingFilters.targetReturnMin !== undefined) count++;
    if (pendingFilters.targetReturnMax !== undefined) count++;
    
    // Investment criteria debt fields
    if (pendingFilters.loanTypes?.length) count++;
    if (pendingFilters.loanProgram?.length) count++;
    if (pendingFilters.structuredLoanTranche?.length) count++;
    if (pendingFilters.recourseLoan?.length) count++;
    if (pendingFilters.eligibleBorrower?.length) count++;
    if (pendingFilters.lienPosition?.length) count++;
    if (pendingFilters.rateLock?.length) count++;
    if (pendingFilters.rateType?.length) count++;
    if (pendingFilters.amortization?.length) count++;
    if (pendingFilters.loanTypeNormalized?.length) count++;
    if (pendingFilters.loanMinDebtYield?.length) count++;
    if (pendingFilters.futureFacilities?.length) count++;
    if (pendingFilters.occupancyRequirements?.length) count++;
    if (pendingFilters.prepayment?.length) count++;
    if (pendingFilters.yieldMaintenance?.length) count++;
    
    // Investment criteria debt range fields
    if (pendingFilters.closingTimeMin !== undefined) count++;
    if (pendingFilters.closingTimeMax !== undefined) count++;
    if (pendingFilters.minLoanDscrMin !== undefined) count++;
    if (pendingFilters.minLoanDscrMax !== undefined) count++;
    if (pendingFilters.maxLoanDscrMin !== undefined) count++;
    if (pendingFilters.maxLoanDscrMax !== undefined) count++;
    if (pendingFilters.loanOriginationMaxFeeMin !== undefined) count++;
    if (pendingFilters.loanOriginationMaxFeeMax !== undefined) count++;
    if (pendingFilters.loanOriginationMinFeeMin !== undefined) count++;
    if (pendingFilters.loanOriginationMinFeeMax !== undefined) count++;
    if (pendingFilters.loanExitMinFeeMin !== undefined) count++;
    if (pendingFilters.loanExitMinFeeMax !== undefined) count++;
    if (pendingFilters.loanExitMaxFeeMin !== undefined) count++;
    if (pendingFilters.loanExitMaxFeeMax !== undefined) count++;
    if (pendingFilters.loanInterestRateSofrMin !== undefined) count++;
    if (pendingFilters.loanInterestRateSofrMax !== undefined) count++;
    if (pendingFilters.loanInterestRateWsjMin !== undefined) count++;
    if (pendingFilters.loanInterestRateWsjMax !== undefined) count++;
    if (pendingFilters.loanInterestRatePrimeMin !== undefined) count++;
    if (pendingFilters.loanInterestRatePrimeMax !== undefined) count++;
    if (pendingFilters.loanInterestRate3ytMin !== undefined) count++;
    if (pendingFilters.loanInterestRate3ytMax !== undefined) count++;
    if (pendingFilters.loanInterestRate5ytMin !== undefined) count++;
    if (pendingFilters.loanInterestRate5ytMax !== undefined) count++;
    if (pendingFilters.loanInterestRate10ytMin !== undefined) count++;
    if (pendingFilters.loanInterestRate10ytMax !== undefined) count++;
    if (pendingFilters.loanInterestRate30ytMin !== undefined) count++;
    if (pendingFilters.loanInterestRate30ytMax !== undefined) count++;
    if (pendingFilters.loanToValueMinMin !== undefined) count++;
    if (pendingFilters.loanToValueMinMax !== undefined) count++;
    if (pendingFilters.loanToValueMaxMin !== undefined) count++;
    if (pendingFilters.loanToValueMaxMax !== undefined) count++;
    if (pendingFilters.loanToCostMinMin !== undefined) count++;
    if (pendingFilters.loanToCostMinMax !== undefined) count++;
    if (pendingFilters.loanToCostMaxMin !== undefined) count++;
    if (pendingFilters.loanToCostMaxMax !== undefined) count++;
    if (pendingFilters.minLoanTermMin !== undefined) count++;
    if (pendingFilters.minLoanTermMax !== undefined) count++;
    if (pendingFilters.maxLoanTermMin !== undefined) count++;
    if (pendingFilters.maxLoanTermMax !== undefined) count++;
    
    // Investment criteria equity fields
    if (pendingFilters.ownershipRequirement?.length) count++;
    if (pendingFilters.minimumYieldOnCostMin !== undefined) count++;
    if (pendingFilters.minimumYieldOnCostMax !== undefined) count++;
    if (pendingFilters.maxLeverageToleranceMin !== undefined) count++;
    if (pendingFilters.maxLeverageToleranceMax !== undefined) count++;
    
    // Investment criteria equity range fields
    if (pendingFilters.targetReturnMin !== undefined) count++;
    if (pendingFilters.targetReturnMax !== undefined) count++;
    if (pendingFilters.minimumIrrMin !== undefined) count++;
    if (pendingFilters.minimumIrrMax !== undefined) count++;
    if (pendingFilters.targetCashOnCashMin !== undefined) count++;
    if (pendingFilters.targetCashOnCashMax !== undefined) count++;
    if (pendingFilters.minHoldPeriodYearsMin !== undefined) count++;
    if (pendingFilters.minHoldPeriodYearsMax !== undefined) count++;
    if (pendingFilters.maxHoldPeriodYearsMin !== undefined) count++;
    if (pendingFilters.maxHoldPeriodYearsMax !== undefined) count++;
    
    // Investment criteria additional fields
    if (pendingFilters.decisionMakingProcess?.length) count++;
    if (pendingFilters.investmentCriteriaNotes?.length) count++;
    
    // Gmail outreach filters
    if (pendingFilters.hasBeenReachedOut !== undefined) count++;
    
    // New filters
    if (pendingFilters.notEmptyEmail !== undefined) count++;
    if (pendingFilters.contactIds?.length) count++;
    if (pendingFilters.contactEmails?.length) count++;
    
    // NOT filters
    if (pendingFilters.notSource?.length) count++;
    if (pendingFilters.notEmailStatus?.length) count++;
    if (pendingFilters.notEmailVerificationStatus?.length) count++;
    if (pendingFilters.notContactEnrichmentV2Status?.length) count++;
    if (pendingFilters.notContactInvestmentCriteriaStatus?.length) count++;
    if (pendingFilters.notEmailGenerationStatus?.length) count++;
    if (pendingFilters.notEmailSendingStatus?.length) count++;
    if (pendingFilters.notCapitalPosition?.length) count++;
    if (pendingFilters.notPropertyTypes?.length) count++;
    if (pendingFilters.notStrategies?.length) count++;
    if (pendingFilters.notContactType?.length) count++;
    if (pendingFilters.notCompanyWebsiteScrapingStatus?.length) count++;
    if (pendingFilters.notCompanyOverviewV2Status?.length) count++;
    if (pendingFilters.notCompanyInvestmentCriteriaStatus?.length) count++;
    if (pendingFilters.notLoanTypes?.length) count++;
    if (pendingFilters.notStructuredLoanTranche?.length) count++;
    if (pendingFilters.notLoanProgram?.length) count++;
    if (pendingFilters.notRecourseLoan?.length) count++;
    if (pendingFilters.notEligibleBorrower?.length) count++;
    if (pendingFilters.notLienPosition?.length) count++;
    if (pendingFilters.notOwnershipRequirement?.length) count++;
    
    return count;
  };

  const activeFilterCount = getActiveFilterCount();

  // Enhanced sort options - Updated to match requirements
  const UNIFIED_SORT_OPTIONS = [
    { value: 'company_name', label: 'Company Name', icon: Building },
    { value: 'created_at', label: 'Created Date', icon: Calendar },
    { value: 'minimum_deal_size', label: 'Deal Size (Min)', icon: DollarSign },
    { value: 'first_name', label: 'First Name', icon: User },
    { value: 'last_name', label: 'Last Name', icon: User },
    { value: 'updated_at', label: 'Last Updated Date', icon: Clock },
  ];



  // Toggle section expansion
  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({ ...prev, [section]: !prev[section] }));
  };

  // Get NOT filter state for specific filter
  const getFilterNotState = (filterKey: string): boolean => {
    return filterNotModes[filterKey] || false;
  };

  // Toggle NOT filter mode for specific filter
  const toggleFilterNotMode = (filterKey: string) => {
    setFilterNotModes(prev => ({
      ...prev,
      [filterKey]: !prev[filterKey]
    }));
  };

  // Check if conditional loan fields should be shown
  const shouldShowLoanFields = () => {
    const capitalPositions = pendingFilters.capitalPosition || [];
    return capitalPositions.some(position => 
      ['Mezzanine', 'Senior Debt', 'Stretch Senior'].includes(position)
    );
  };

  // Check if debt fields should be shown based on capital position
  const shouldShowDebtFields = () => {
    const capitalPositions = pendingFilters.capitalPosition || [];
    return capitalPositions.some(position => 
      ['Mezzanine', 'Senior Debt', 'Stretch Senior', 'Debt'].includes(position)
    );
  };

  // Check if equity fields should be shown based on capital position
  const shouldShowEquityFields = () => {
    const capitalPositions = pendingFilters.capitalPosition || [];
    return capitalPositions.some(position => 
      ['Equity', 'Preferred Equity', 'Joint Venture'].includes(position)
    );
  };

  // Role-based section visibility
  const canShowSection = (section: string): boolean => {
    if (userRole === 'admin') return true;
    
    if (userRole === 'user') {
      const allowedUserSections = ['search', 'investment_criteria'];
      return allowedUserSections.includes(section);
    }
    
    // Guest can see all sections but read-only
    return userRole === 'guest';
  };

  // Check if user should see only basic IC fields
  const shouldShowBasicICOnly = (): boolean => {
    return userRole === 'user';
  };

  // Multi-select component with NOT filter support
  const EnhancedMultiSelect = ({ 
    options, 
    selected, 
    notSelected,
    onChange, 
    onNotChange,
    placeholder, 
    disabled,
    label,
    showNotFilter = false,
    filterKey
  }: {
    options: Array<{value: string, label: string}>,
    selected: string[],
    notSelected?: string[],
    onChange: (values: string[]) => void,
    onNotChange?: (values: string[]) => void,
    placeholder: string,
    disabled?: boolean,
    label: string,
    showNotFilter?: boolean,
    filterKey: string
  }) => {
    const isNotMode = getFilterNotState(filterKey);
    const isReadOnly = userRole === 'guest';
    const isDisabled = disabled || isReadOnly;
    
    return (
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <Label className="text-sm font-medium text-gray-700">{label}</Label>
          {showNotFilter && userRole === 'admin' && (
            <div className="flex items-center gap-2">
              <EyeIcon className="h-4 w-4 text-gray-400" />
              <Switch
                checked={isNotMode}
                onCheckedChange={() => toggleFilterNotMode(filterKey)}
                disabled={isReadOnly}
              />
              <EyeOff className="h-4 w-4 text-gray-400" />
            </div>
          )}
        </div>
        
        {!isNotMode ? (
          <ReactMultiSelect
            options={options}
            selected={selected || []}
            onChange={isReadOnly ? () => {} : onChange}
            placeholder={isReadOnly ? `${placeholder} (Read Only)` : placeholder}
            disabled={isDisabled}
            showSelectAll={!isReadOnly}
            selectAllLabel="Select All"
          />
        ) : (
          <ReactMultiSelect
            options={options}
            selected={notSelected || []}
            onChange={isReadOnly ? () => {} : (onNotChange || (() => {}))}
            placeholder={isReadOnly ? `NOT ${placeholder.toLowerCase()} (Read Only)` : `NOT ${placeholder.toLowerCase()}`}
            disabled={isDisabled}
            showSelectAll={!isReadOnly}
            selectAllLabel="Exclude All"
          />
        )}
      </div>
    );
  };

  return (
    <>
      {/* Unified Filter Bar V2 */}
      <div className="w-full bg-white border border-gray-200 rounded-xl shadow-sm mb-6">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-4">
            {/* Unified Filter Button V2 */}
            <Button
              onClick={() => setIsFilterPanelOpen(!isFilterPanelOpen)}
              className={`flex items-center gap-3 px-6 py-3 rounded-lg shadow-md hover:shadow-lg transition-all duration-200 ${
                isFilterPanelOpen 
                  ? 'bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white' 
                  : 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white'
              }`}
            >
              <Database className="h-5 w-5" />
              <span className="font-medium">Filters</span>
              {/* <Badge className="bg-amber-100 text-amber-800 border border-amber-200 ml-1 font-semibold">
                Enhanced
              </Badge> */}
              {activeFilterCount > 0 && (
                <Badge className="bg-white/20 text-white border border-white/20 ml-1">
                  {activeFilterCount}
                </Badge>
              )}
            </Button>

            {/* Search Bar */}
            <div className="flex items-center gap-2">
              <Select 
                value={searchCriteria} 
                onValueChange={(value: 'fullNameSearch' | 'emailSearch' | 'companyNameSearch') => setSearchCriteria(value)}
              >
                <SelectTrigger className="w-auto min-w-[140px] border-gray-200 bg-white shadow-sm">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="fullNameSearch">
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4" />
                      Name
                    </div>
                  </SelectItem>
                  <SelectItem value="emailSearch">
                    <div className="flex items-center gap-2">
                      <Mail className="h-4 w-4" />
                      Email
                    </div>
                  </SelectItem>
                  <SelectItem value="companyNameSearch">
                    <div className="flex items-center gap-2">
                      <Building className="h-4 w-4" />
                      Company
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
              
              <Input
                type="text"
                placeholder={`Search by ${searchCriteria === 'fullNameSearch' ? 'name' : searchCriteria === 'emailSearch' ? 'email' : 'company name'}...`}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-64 border-gray-200 bg-white shadow-sm"
              />
              
              {searchTerm && (
                <Button
                  onClick={() => {
                    setSearchTerm('');
                    setSearchCriteria('fullNameSearch');
                  }}
                  className="text-gray-500 hover:text-red-600 bg-transparent border border-gray-200 hover:border-red-200 hover:bg-red-50 px-3 py-1 text-sm"
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>

          <div className="flex items-center gap-3">
            {/* Active Filters Count */}
            {activeFilterCount > 0 && (
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600">
                  {activeFilterCount} filter{activeFilterCount !== 1 ? 's' : ''} applied
                </span>
                <Button
                  onClick={handleClearFilters}
                  className="text-gray-500 hover:text-red-600 bg-transparent border border-gray-200 hover:border-red-200 hover:bg-red-50 px-3 py-1 text-sm"
                >
                  <RotateCcw className="h-4 w-4 mr-1" />
                  Clear All
                </Button>
              </div>
            )}

            {/* Enhanced Sort Controls */}
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2">
                <BarChart3 className="h-4 w-4 text-gray-500" />
                <span className="text-sm font-medium text-gray-700">Sort by:</span>
              </div>
              <div className="flex items-center gap-2">
                <Select 
                  value={pendingFilters.sortBy || 'updated_at'} 
                  onValueChange={(value) => {
                    // Instant sorting - apply immediately when field changes
                    const newSortBy = value;
                    const newSortOrder: 'asc' | 'desc' = 'asc'; // Default to ascending on field change
                    const updatedFilters = {
                      ...pendingFilters,
                      sortBy: newSortBy,
                      sortOrder: newSortOrder,
                      page: 1 // Reset to first page when sorting
                    };
                    setPendingFilters(updatedFilters);
                    // Apply filters immediately for instant sorting
                    setLocalFilters(updatedFilters);
                    onFiltersChange(updatedFilters);
                  }}
                >
                  <SelectTrigger className="w-auto min-w-[250px] border-gray-200 bg-white shadow-sm">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="max-h-[400px]">
                    {UNIFIED_SORT_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        <div className="flex items-center gap-2">
                          <option.icon className="h-4 w-4" />
                          {option.label}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                
                {/* Sort Order Toggle */}
                <Button
                  onClick={() => {
                    // Instant sort order toggle - apply immediately
                    const newSortOrder: 'asc' | 'desc' = pendingFilters.sortOrder === 'asc' ? 'desc' : 'asc';
                    const updatedFilters = {
                      ...pendingFilters,
                      sortOrder: newSortOrder,
                      page: 1 // Reset to first page when changing sort order
                    };
                    setPendingFilters(updatedFilters);
                    // Apply filters immediately for instant sorting
                    setLocalFilters(updatedFilters);
                    onFiltersChange(updatedFilters);
                  }}
                  className={`p-2 rounded-lg border transition-all ${
                    pendingFilters.sortOrder === 'asc' 
                      ? 'bg-purple-50 border-purple-200 text-purple-700 hover:bg-purple-100' 
                      : 'bg-gray-50 border-gray-200 text-gray-600 hover:bg-gray-100'
                  }`}
                  title={`Sort ${pendingFilters.sortOrder === 'asc' ? 'Ascending' : 'Descending'}`}
                >
                  {pendingFilters.sortOrder === 'asc' ? (
                    <ArrowUp className="h-4 w-4" />
                  ) : (
                    <ArrowDown className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Unified Right Side Filter Panel V2 */}
      <div className={`fixed top-0 right-0 h-full w-[800px] bg-white border-l border-gray-200 shadow-xl transform transition-transform duration-300 ease-in-out z-50 ${
        isFilterPanelOpen ? 'translate-x-0' : 'translate-x-full'
      }`}>
        <div className="flex flex-col h-full">
          {/* Enhanced Panel Header V2 */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-purple-50 via-indigo-50 to-blue-50">
            <div className="flex items-center gap-4">
              <div className="relative">
                <div className="p-3 rounded-xl bg-gradient-to-r from-purple-600 to-indigo-600 text-white shadow-lg">
                  <Database className="h-6 w-6" />
                </div>
                {activeFilterCount > 0 && (
                  <div className="absolute -top-2 -right-2 h-5 w-5 rounded-full bg-red-500 text-white text-xs flex items-center justify-center font-bold">
                    {activeFilterCount > 9 ? '9+' : activeFilterCount}
                  </div>
                )}
              </div>
              <div>
                <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
                  Filters
                  <Badge className="bg-amber-100 text-amber-800 border border-amber-200 ml-2 font-semibold">
                    Enhanced Analytics
                  </Badge>
                </h2>
                <p className="text-sm text-gray-600 mt-1 flex items-center gap-2">
                  <Table className="h-4 w-4" />
                  Advanced multi-table contact filtering with insights data
                </p>
              </div>
            </div>
            <Button
              onClick={() => setIsFilterPanelOpen(false)}
              className="text-gray-500 hover:text-gray-700 bg-white border border-gray-200 p-2 hover:bg-gray-50 transition-colors"
            >
              <X className="h-5 w-5" />
            </Button>
          </div>

          {/* Panel Content - Scrollable */}
          <div className="flex-1 overflow-y-auto p-6 space-y-6">

            {/* Separate Search Fields */}
            <Card className="border-0 shadow-sm bg-gradient-to-r from-gray-50 to-blue-50">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-3 text-lg">
                  <Search className="h-5 w-5 text-blue-600" />
                  Search Filters
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 gap-4">
                  <div className="relative">
                    <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search by full name..."
                      value={pendingFilters.fullNameSearch || ''}
                      onChange={(e) => updatePendingFilters({ fullNameSearch: e.target.value || undefined })}
                      onKeyDown={(e) => e.key === 'Enter' && applyFilters()}
                      className="pl-10 border-gray-200 focus:border-purple-500 focus:ring-1 focus:ring-purple-500"
                      disabled={isLoading}
                    />
                  </div>
                  
                  <div className="relative">
                    <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search by email address..."
                      value={pendingFilters.emailSearch || ''}
                      onChange={(e) => updatePendingFilters({ emailSearch: e.target.value || undefined })}
                      onKeyDown={(e) => e.key === 'Enter' && applyFilters()}
                      className="pl-10 border-gray-200 focus:border-purple-500 focus:ring-1 focus:ring-purple-500"
                      disabled={isLoading}
                    />
                  </div>
                  
                  <div className="relative">
                    <Building className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search by company name..."
                      value={pendingFilters.companyNameSearch || ''}
                      onChange={(e) => updatePendingFilters({ companyNameSearch: e.target.value || undefined })}
                      onKeyDown={(e) => e.key === 'Enter' && applyFilters()}
                      className="pl-10 border-gray-200 focus:border-purple-500 focus:ring-1 focus:ring-purple-500"
                      disabled={isLoading}
                    />
                  </div>
                </div>
                <p className="text-xs text-gray-500">
                  Targeted search across specific contact fields
                </p>
              </CardContent>
            </Card>

            {/* Core Contacts Table Filters - Admin Only */}
            {canShowSection('contacts') && (
            <Card className="border-0 shadow-sm bg-gradient-to-r from-cyan-50 to-blue-50">
              <CardHeader 
                className="pb-4 cursor-pointer hover:bg-cyan-100/50 transition-colors rounded-t-lg" 
                onClick={() => toggleSection('contacts')}
                title={`Click to ${expandedSections.contacts ? 'collapse' : 'expand'} Core Contact filters`}
              >
                <CardTitle className="flex items-center justify-between text-lg">
                  <div className="flex items-center gap-3">
                    <User className="h-5 w-5 text-cyan-600" />
                    Core Contacts Table
                    <Badge className="bg-cyan-100 text-cyan-700 border border-cyan-200">
                      Primary Data
                    </Badge>
                    <Badge className="bg-gray-100 text-gray-600 border border-gray-200 text-xs">
                      {expandedSections.contacts ? 'Expanded' : 'Click to expand'}
                    </Badge>
                  </div>
                  <ChevronRight className={`h-5 w-5 transition-transform ${expandedSections.contacts ? 'rotate-90' : ''}`} />
                </CardTitle>
              </CardHeader>
              {expandedSections.contacts && (
                <CardContent className="space-y-6">
                  {/* Source Filter */}
                  <EnhancedMultiSelect
                    label="Source"
                    options={sources}
                    selected={pendingFilters.source || []}
                    notSelected={pendingFilters.notSource || []}
                    onChange={(selected: string[]) => updatePendingFilters({ source: selected })}
                    onNotChange={(selected: string[]) => updatePendingFilters({ notSource: selected })}
                    placeholder="Select sources..."
                    disabled={isLoading || loadingSources}
                    showNotFilter={true}
                    filterKey="source"
                  />

                  {/* Email Status */}
                  <EnhancedMultiSelect
                    label="Email Status"
                    options={emailStatus}
                    selected={pendingFilters.emailStatus || []}
                    notSelected={pendingFilters.notEmailStatus || []}
                    onChange={(selected: string[]) => updatePendingFilters({ emailStatus: selected })}
                    onNotChange={(selected: string[]) => updatePendingFilters({ notEmailStatus: selected })}
                    placeholder={loadingEmailStatus ? "Loading email statuses..." : "Select email statuses..."}
                    disabled={isLoading || loadingEmailStatus}
                    showNotFilter={true}
                    filterKey="emailStatus"
                  />

                  {/* Job Tier Filter */}
                  <div className="space-y-3">
                    <Label className="text-sm font-medium text-gray-700">Job Tier</Label>
                    {loadingJobTiers ? (
                      <div className="flex items-center gap-2 text-sm text-gray-500">
                        <Loader2 className="h-4 w-4 animate-spin" />
                        Loading job tiers...
                      </div>
                    ) : (
                      <ReactMultiSelect
                        options={jobTiers}
                        selected={pendingFilters.jobTier || []}
                        onChange={(selected: string[]) => updatePendingFilters({ jobTier: selected })}
                        placeholder="Select job tiers..."
                        disabled={isLoading}
                        showSelectAll={true}
                        selectAllLabel="Select All Job Tiers"
                      />
                    )}
                  </div>

                  {/* Contact Location */}
                  <div className="space-y-3">
                    <Label className="text-sm font-medium text-gray-700">Contact Location</Label>
                    <div className="space-y-2">
                      <Input
                        placeholder="Country"
                        value={pendingFilters.contactCountries?.[0] || ''}
                        onChange={(e) => updatePendingFilters({ contactCountries: e.target.value ? [e.target.value] : undefined })}
                        className="bg-white border-gray-200"
                        disabled={isLoading}
                      />
                      <Input
                        placeholder="State"
                        value={pendingFilters.contactStates?.[0] || ''}
                        onChange={(e) => updatePendingFilters({ contactStates: e.target.value ? [e.target.value] : undefined })}
                        className="bg-white border-gray-200"
                        disabled={isLoading}
                      />
                      <Input
                        placeholder="City"
                        value={pendingFilters.contactCities?.[0] || ''}
                        onChange={(e) => updatePendingFilters({ contactCities: e.target.value ? [e.target.value] : undefined })}
                        className="bg-white border-gray-200"
                        disabled={isLoading}
                      />
                    </div>
                  </div>

                  {/* New Filters - Email and Multi-ID */}
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-3 border rounded-lg bg-blue-50">
                      <div className="flex items-center gap-2">
                        <Mail className="h-4 w-4 text-blue-600" />
                        <span className="text-sm font-medium">Has Non-Empty Email</span>
                      </div>
                      <Switch
                        checked={pendingFilters.notEmptyEmail === true}
                        onCheckedChange={(checked) => updatePendingFilters({ notEmptyEmail: checked || undefined })}
                        disabled={isLoading}
                      />
                    </div>

                    <div className="space-y-3">
                      <Label className="text-sm font-medium text-gray-700">Contact IDs (comma-separated)</Label>
                      <Input
                        placeholder="Enter contact IDs separated by commas (e.g., 1,2,3)"
                        value={pendingFilters.contactIds?.join(',') || ''}
                        onChange={(e) => updatePendingFilters({ 
                          contactIds: e.target.value ? e.target.value.split(',').map(id => id.trim()).filter(Boolean) : undefined 
                        })}
                        className="bg-white border-gray-200"
                        disabled={isLoading}
                      />
                      <p className="text-xs text-gray-500">
                        Filter by specific contact IDs. Enter multiple IDs separated by commas.
                      </p>
                    </div>

                    <div className="space-y-3">
                      <Label className="text-sm font-medium text-gray-700">Contact Emails (comma-separated)</Label>
                      <Input
                        placeholder="Enter email addresses separated by commas"
                        value={pendingFilters.contactEmails?.join(',') || ''}
                        onChange={(e) => updatePendingFilters({ 
                          contactEmails: e.target.value ? e.target.value.split(',').map(email => email.trim()).filter(Boolean) : undefined 
                        })}
                        className="bg-white border-gray-200"
                        disabled={isLoading}
                      />
                      <p className="text-xs text-gray-500">
                        Filter by specific email addresses. Enter multiple emails separated by commas.
                      </p>
                    </div>
                  </div>

                  {/* Processing Status Groups */}
                  <div className="grid grid-cols-3 gap-4">
                    <div className="space-y-3">
                      <Label className="text-sm font-medium text-gray-700">Email Verification</Label>
                      <EnhancedMultiSelect
                        label=""
                        options={processingStatusOptions.map(option => ({ 
                          value: option.value, 
                          label: option.label 
                        }))}
                        selected={pendingFilters.emailVerificationStatus || []}
                        notSelected={pendingFilters.notEmailVerificationStatus || []}
                        onChange={(selected: string[]) => updatePendingFilters({ emailVerificationStatus: selected })}
                        onNotChange={(selected: string[]) => updatePendingFilters({ notEmailVerificationStatus: selected })}
                        placeholder="Select status..."
                        disabled={isLoading}
                        showNotFilter={true}
                        filterKey="emailVerificationStatus"
                      />
                    </div>
                    <div className="space-y-3">
                      <Label className="text-sm font-medium text-gray-700">Contact Enrichment</Label>
                      <EnhancedMultiSelect
                        label=""
                        options={processingStatusOptions.map(option => ({ 
                          value: option.value, 
                          label: option.label 
                        }))}
                        selected={pendingFilters.contactEnrichmentV2Status || []}
                        notSelected={pendingFilters.notContactEnrichmentV2Status || []}
                        onChange={(selected: string[]) => updatePendingFilters({ contactEnrichmentV2Status: selected })}
                        onNotChange={(selected: string[]) => updatePendingFilters({ notContactEnrichmentV2Status: selected })}
                        placeholder="Select status..."
                        disabled={isLoading}
                        showNotFilter={true}
                        filterKey="contactEnrichmentV2Status"
                      />
                    </div>
                    <div className="space-y-3">
                      <Label className="text-sm font-medium text-gray-700">Investment Criteria</Label>
                      <EnhancedMultiSelect
                        label=""
                        options={processingStatusOptions.map(option => ({ 
                          value: option.value, 
                          label: option.label 
                        }))}
                        selected={pendingFilters.contactInvestmentCriteriaStatus || []}
                        notSelected={pendingFilters.notContactInvestmentCriteriaStatus || []}
                        onChange={(selected: string[]) => updatePendingFilters({ contactInvestmentCriteriaStatus: selected })}
                        onNotChange={(selected: string[]) => updatePendingFilters({ notContactInvestmentCriteriaStatus: selected })}
                        placeholder="Select status..."
                        disabled={isLoading}
                        showNotFilter={true}
                        filterKey="contactInvestmentCriteriaStatus"
                      />
                    </div>
                  </div>
                  
                  {/* Email Processing Status Groups */}
                  <div className="grid grid-cols-3 gap-4">
                    <div className="space-y-3">
                      <EnhancedMultiSelect
                        label="Email Generation"
                        options={processingStatusOptions.map(option => ({ 
                          value: option.value, 
                          label: option.label 
                        }))}
                        selected={pendingFilters.emailGenerationStatus || []}
                        notSelected={pendingFilters.notEmailGenerationStatus || []}
                        onChange={(selected: string[]) => updatePendingFilters({ emailGenerationStatus: selected })}
                        onNotChange={(selected: string[]) => updatePendingFilters({ notEmailGenerationStatus: selected })}
                        placeholder="Select status..."
                        disabled={isLoading}
                        showNotFilter={true}
                        filterKey="emailGenerationStatus"
                      />
                    </div>
                    <div className="space-y-3">
                      <EnhancedMultiSelect
                        label="Email Sending"
                        options={processingStatusOptions.map(option => ({ 
                          value: option.value, 
                          label: option.label 
                        }))}
                        selected={pendingFilters.emailSendingStatus || []}
                        notSelected={pendingFilters.notEmailSendingStatus || []}
                        onChange={(selected: string[]) => updatePendingFilters({ emailSendingStatus: selected })}
                        onNotChange={(selected: string[]) => updatePendingFilters({ notEmailSendingStatus: selected })}
                        placeholder="Select status..."
                        disabled={isLoading}
                        showNotFilter={true}
                        filterKey="emailSendingStatus"
                      />
                    </div>
                    <div className="space-y-3">
                      <EnhancedMultiSelect
                        label="Sync Status"
                        options={smartleadSyncStatusOptions.map(option => ({ 
                          value: option.value, 
                          label: option.label 
                        }))}
                        selected={pendingFilters.smartleadSyncStatus || []}
                        notSelected={pendingFilters.notSmartleadSyncStatus || []}
                        onChange={(selected: string[]) => updatePendingFilters({ smartleadSyncStatus: selected })}
                        onNotChange={(selected: string[]) => updatePendingFilters({ notSmartleadSyncStatus: selected })}
                        placeholder="Select sync status..."
                        disabled={isLoading}
                        showNotFilter={true}
                        filterKey="smartleadSyncStatus"
                      />
                    </div>
                  </div>
                </CardContent>
              )}
            </Card>
            )}

            {/* Contact Enrichment V2 Filters - Admin Only */}
            {canShowSection('enrichment_v2') && (
            <Card className="border-0 shadow-sm bg-gradient-to-r from-emerald-50 to-teal-50">
              <CardHeader 
                className="pb-4 cursor-pointer hover:bg-emerald-100/50 transition-colors rounded-t-lg" 
                onClick={() => toggleSection('contact_enrichment_v2')}
                title={`Click to ${expandedSections.contact_enrichment_v2 ? 'collapse' : 'expand'} Contact Enrichment V2 filters`}
              >
                <CardTitle className="flex items-center justify-between text-lg">
                  <div className="flex items-center gap-3">
                    <Brain className="h-5 w-5 text-emerald-600" />
                    Contact Enrichment
                    <Badge className="bg-emerald-100 text-emerald-700 border border-emerald-200">
                      AI Enhanced
                    </Badge>
                    <Badge className="bg-amber-100 text-amber-800 border border-amber-200 text-xs font-semibold">
                      NEW
                    </Badge>
                    <Badge className="bg-gray-100 text-gray-600 border border-gray-200 text-xs">
                      {expandedSections.contact_enrichment_v2 ? 'Expanded' : 'Click to expand'}
                    </Badge>
                  </div>
                  <ChevronRight className={`h-5 w-5 transition-transform ${expandedSections.contact_enrichment_v2 ? 'rotate-90' : ''}`} />
                </CardTitle>
              </CardHeader>
              {expandedSections.contact_enrichment_v2 && (
                <CardContent className="space-y-6">
                  {/* Contact Type */}
                  <EnhancedMultiSelect
                    label="Contact Type"
                    options={loadingEnrichmentV2Options ? [] : enrichmentV2Options.contactTypes}
                    selected={pendingFilters.contactType || []}
                    notSelected={pendingFilters.notContactType || []}
                    onChange={(selected: string[]) => updatePendingFilters({ contactType: selected })}
                    onNotChange={(selected: string[]) => updatePendingFilters({ notContactType: selected })}
                    placeholder={loadingEnrichmentV2Options ? "Loading contact types..." : "Select contact types..."}
                    disabled={isLoading || loadingEnrichmentV2Options}
                    showNotFilter={true}
                    filterKey="contactType"
                  />

                  {/* Relationship Owner */}
                  <div className="space-y-3">
                    <Label className="text-sm font-medium text-gray-700">Relationship Owner</Label>
                    <ReactMultiSelect
                      options={loadingEnrichmentV2Options ? [] : enrichmentV2Options.relationshipOwners}
                      selected={pendingFilters.relationshipOwner || []}
                      onChange={(selected: string[]) => updatePendingFilters({ relationshipOwner: selected })}
                      placeholder={loadingEnrichmentV2Options ? "Loading relationship owners..." : "Select relationship owners..."}
                      disabled={isLoading || loadingEnrichmentV2Options}
                      showSelectAll={true}
                      selectAllLabel="Select All Relationship Owners"
                    />
                  </div>

                  {/* Role in Decision Making */}
                  <div className="space-y-3">
                    <Label className="text-sm font-medium text-gray-700">Role in Decision Making</Label>
                    <ReactMultiSelect
                      options={loadingEnrichmentV2Options ? [] : enrichmentV2Options.rolesInDecisionMaking}
                      selected={pendingFilters.roleInDecisionMaking || []}
                      onChange={(selected: string[]) => updatePendingFilters({ roleInDecisionMaking: selected })}
                      placeholder={loadingEnrichmentV2Options ? "Loading decision making roles..." : "Select decision making roles..."}
                      disabled={isLoading || loadingEnrichmentV2Options}
                      showSelectAll={true}
                      selectAllLabel="Select All Decision Making Roles"
                    />
                  </div>

                  {/* Source of Introduction */}
                  <div className="space-y-3">
                    <Label className="text-sm font-medium text-gray-700">Source of Introduction</Label>
                    <ReactMultiSelect
                      options={loadingEnrichmentV2Options ? [] : enrichmentV2Options.sourcesOfIntroduction}
                      selected={pendingFilters.sourceOfIntroduction || []}
                      onChange={(selected: string[]) => updatePendingFilters({ sourceOfIntroduction: selected })}
                      placeholder={loadingEnrichmentV2Options ? "Loading introduction sources..." : "Select introduction sources..."}
                      disabled={isLoading || loadingEnrichmentV2Options}
                      showSelectAll={true}
                      selectAllLabel="Select All Introduction Sources"
                    />
                  </div>

                  {/* Boolean V2 Enrichment Filters */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-3">
                      <Label className="text-sm font-medium text-gray-700">Data Availability Filters</Label>
                      <div className="space-y-2">
                        <div className="flex items-center justify-between p-2 border rounded-lg">
                          <span className="text-sm">Has Executive Summary</span>
                          <Switch
                            checked={pendingFilters.hasExecutiveSummary === true}
                            onCheckedChange={(checked) => updatePendingFilters({ hasExecutiveSummary: checked || undefined })}
                            disabled={isLoading}
                          />
                        </div>
                        <div className="flex items-center justify-between p-2 border rounded-lg">
                          <span className="text-sm">Has Career Timeline</span>
                          <Switch
                            checked={pendingFilters.hasCareerTimeline === true}
                            onCheckedChange={(checked) => updatePendingFilters({ hasCareerTimeline: checked || undefined })}
                            disabled={isLoading}
                          />
                        </div>
                        <div className="flex items-center justify-between p-2 border rounded-lg">
                          <span className="text-sm">Has Additional Email</span>
                          <Switch
                            checked={pendingFilters.hasAdditionalEmail === true}
                            onCheckedChange={(checked) => updatePendingFilters({ hasAdditionalEmail: checked || undefined })}
                            disabled={isLoading}
                          />
                        </div>
                        <div className="flex items-center justify-between p-2 border rounded-lg">
                          <span className="text-sm">Has Secondary Phone</span>
                          <Switch
                            checked={pendingFilters.hasSecondaryPhone === true}
                            onCheckedChange={(checked) => updatePendingFilters({ hasSecondaryPhone: checked || undefined })}
                            disabled={isLoading}
                          />
                        </div>
                      </div>
                    </div>

                    <div className="space-y-3">
                      <Label className="text-sm font-medium text-gray-700">Social Media Presence</Label>
                      <div className="space-y-2">
                        <div className="flex items-center justify-between p-2 border rounded-lg">
                          <div className="flex items-center gap-2">
                            <TwitterIcon className="h-4 w-4 text-blue-500" />
                            <span className="text-sm">Has Twitter</span>
                          </div>
                          <Switch
                            checked={pendingFilters.hasTwitter === true}
                            onCheckedChange={(checked) => updatePendingFilters({ hasTwitter: checked || undefined })}
                            disabled={isLoading}
                          />
                        </div>
                        <div className="flex items-center justify-between p-2 border rounded-lg">
                          <div className="flex items-center gap-2">
                            <Facebook className="h-4 w-4 text-blue-600" />
                            <span className="text-sm">Has Facebook</span>
                          </div>
                          <Switch
                            checked={pendingFilters.hasFacebook === true}
                            onCheckedChange={(checked) => updatePendingFilters({ hasFacebook: checked || undefined })}
                            disabled={isLoading}
                          />
                        </div>
                        <div className="flex items-center justify-between p-2 border rounded-lg">
                          <div className="flex items-center gap-2">
                            <Instagram className="h-4 w-4 text-pink-500" />
                            <span className="text-sm">Has Instagram</span>
                          </div>
                          <Switch
                            checked={pendingFilters.hasInstagram === true}
                            onCheckedChange={(checked) => updatePendingFilters({ hasInstagram: checked || undefined })}
                            disabled={isLoading}
                          />
                        </div>
                        <div className="flex items-center justify-between p-2 border rounded-lg">
                          <div className="flex items-center gap-2">
                            <Youtube className="h-4 w-4 text-red-500" />
                            <span className="text-sm">Has YouTube</span>
                          </div>
                          <Switch
                            checked={pendingFilters.hasYoutube === true}
                            onCheckedChange={(checked) => updatePendingFilters({ hasYoutube: checked || undefined })}
                            disabled={isLoading}
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Education Filters */}
                  <div className="space-y-4">
                    <div className="flex items-center gap-2">
                      <GraduationCap className="h-5 w-5 text-blue-600" />
                      <h4 className="font-medium text-gray-800">Education Information</h4>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-3">
                        <Label className="text-sm font-medium text-gray-700">Education College</Label>
                        <ReactMultiSelect
                          options={loadingEnrichmentV2Options ? [] : enrichmentV2Options.educationColleges}
                          selected={pendingFilters.educationCollege || []}
                          onChange={(selected: string[]) => updatePendingFilters({ educationCollege: selected })}
                          placeholder={loadingEnrichmentV2Options ? "Loading colleges..." : "Select colleges..."}
                          disabled={isLoading || loadingEnrichmentV2Options}
                          showSelectAll={true}
                          selectAllLabel="Select All Colleges"
                        />
                      </div>
                      
                      <div className="space-y-3">
                        <Label className="text-sm font-medium text-gray-700">Education College Year Graduated</Label>
                        <ReactMultiSelect
                          options={loadingEnrichmentV2Options ? [] : enrichmentV2Options.educationCollegeYearGraduated || []}
                          selected={pendingFilters.educationCollegeYearGraduated || []}
                          onChange={(selected: string[]) => updatePendingFilters({ educationCollegeYearGraduated: selected })}
                          placeholder={loadingEnrichmentV2Options ? "Loading graduation years..." : "Select graduation years..."}
                          disabled={isLoading || loadingEnrichmentV2Options}
                          showSelectAll={true}
                          selectAllLabel="Select All Graduation Years"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Personal Details */}
                  <div className="space-y-4">
                    <div className="flex items-center gap-2">
                      <User className="h-5 w-5 text-purple-600" />
                      <h4 className="font-medium text-gray-800">Personal Details</h4>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <div className="flex items-center justify-between p-2 border rounded-lg">
                          <div className="flex items-center gap-2">
                            <Trophy className="h-4 w-4 text-yellow-500" />
                            <span className="text-sm">Has Achievements</span>
                          </div>
                          <Switch
                            checked={pendingFilters.hasHonorableAchievements === true}
                            onCheckedChange={(checked) => updatePendingFilters({ hasHonorableAchievements: checked || undefined })}
                            disabled={isLoading}
                          />
                        </div>
                        <div className="flex items-center justify-between p-2 border rounded-lg">
                          <div className="flex items-center gap-2">
                            <Heart className="h-4 w-4 text-red-500" />
                            <span className="text-sm">Has Hobbies</span>
                          </div>
                          <Switch
                            checked={pendingFilters.hasHobbies === true}
                            onCheckedChange={(checked) => updatePendingFilters({ hasHobbies: checked || undefined })}
                            disabled={isLoading}
                          />
                        </div>
                      </div>
                      
                      <div className="space-y-2">
                        <div className="flex items-center justify-between p-2 border rounded-lg">
                          <div className="flex items-center gap-2">
                            <MapPinIcon className="h-4 w-4 text-green-500" />
                            <span className="text-sm">Has Contact Address</span>
                          </div>
                          <Switch
                            checked={pendingFilters.hasContactAddress === true}
                            onCheckedChange={(checked) => updatePendingFilters({ hasContactAddress: checked || undefined })}
                            disabled={isLoading}
                          />
                        </div>
                        <div className="flex items-center justify-between p-2 border rounded-lg">
                          <span className="text-sm">Has Zip Code</span>
                          <Switch
                            checked={pendingFilters.hasContactZipCode === true}
                            onCheckedChange={(checked) => updatePendingFilters({ hasContactZipCode: checked || undefined })}
                            disabled={isLoading}
                          />
                        </div>
                      </div>
                    </div>

                    {/* Age Range Filter */}
                    <div className="space-y-3">
                      <Label className="text-sm font-medium text-gray-700">Age Range</Label>
                      <ReactMultiSelect
                        options={loadingEnrichmentV2Options ? [] : enrichmentV2Options.ageRanges}
                        selected={pendingFilters.ageRange || []}
                        onChange={(selected: string[]) => updatePendingFilters({ ageRange: selected })}
                        placeholder={loadingEnrichmentV2Options ? "Loading age ranges..." : "Select age ranges..."}
                        disabled={isLoading || loadingEnrichmentV2Options}
                        showSelectAll={true}
                        selectAllLabel="Select All Age Ranges"
                      />
                    </div>

                    {/* Accredited Investor Status */}
                    <div className="flex items-center justify-between p-3 border rounded-lg bg-amber-50">
                      <div className="flex items-center gap-2">
                        <Shield className="h-4 w-4 text-amber-600" />
                        <span className="text-sm font-medium">Accredited Investor Status</span>
                      </div>
                      <Switch
                        checked={pendingFilters.accreditedInvestorStatus === true}
                        onCheckedChange={(checked) => updatePendingFilters({ accreditedInvestorStatus: checked || undefined })}
                        disabled={isLoading}
                      />
                    </div>
                  </div>
                </CardContent>
              )}
            </Card>
            )}

            {/* Company Processors Filters - Admin Only */}
            {canShowSection('company_processors') && (
            <Card className="border-0 shadow-sm bg-gradient-to-r from-orange-50 to-red-50">
              <CardHeader 
                className="pb-4 cursor-pointer hover:bg-orange-100/50 transition-colors rounded-t-lg" 
                onClick={() => toggleSection('company_processors')}
                title={`Click to ${expandedSections.company_processors ? 'collapse' : 'expand'} Company Processors filters`}
              >
                <CardTitle className="flex items-center justify-between text-lg">
                  <div className="flex items-center gap-3">
                    <Factory className="h-5 w-5 text-orange-600" />
                    Company Processors Table
                    <Badge className="bg-orange-100 text-orange-700 border border-orange-200">
                      Related Company Data
                    </Badge>
                    <Badge className="bg-gray-100 text-gray-600 border border-gray-200 text-xs">
                      {expandedSections.company_processors ? 'Expanded' : 'Click to expand'}
                    </Badge>
                  </div>
                  <ChevronRight className={`h-5 w-5 transition-transform ${expandedSections.company_processors ? 'rotate-90' : ''}`} />
                </CardTitle>
              </CardHeader>
              {expandedSections.company_processors && (
                <CardContent className="space-y-6">
                  {/* Company Processing Status Groups */}
                  <div className="grid grid-cols-3 gap-4">
                    <div className="space-y-3">
                      <Label className="text-sm font-medium text-gray-700">Website Scraping</Label>
                      <EnhancedMultiSelect
                        label=""
                        options={processingStatusOptions.map(option => ({ 
                          value: option.value, 
                          label: option.label 
                        }))}
                        selected={pendingFilters.companyWebsiteScrapingStatus || []}
                        notSelected={pendingFilters.notCompanyWebsiteScrapingStatus || []}
                        onChange={(selected: string[]) => updatePendingFilters({ companyWebsiteScrapingStatus: selected })}
                        onNotChange={(selected: string[]) => updatePendingFilters({ notCompanyWebsiteScrapingStatus: selected })}
                        placeholder="Select status..."
                        disabled={isLoading}
                        showNotFilter={true}
                        filterKey="companyWebsiteScrapingStatus"
                      />
                    </div>
                    <div className="space-y-3">
                      <Label className="text-sm font-medium text-gray-700">Company Overview V2</Label>
                      <EnhancedMultiSelect
                        label=""
                        options={processingStatusOptions.map(option => ({ 
                          value: option.value, 
                          label: option.label 
                        }))}
                        selected={pendingFilters.companyOverviewV2Status || []}
                        notSelected={pendingFilters.notCompanyOverviewV2Status || []}
                        onChange={(selected: string[]) => updatePendingFilters({ companyOverviewV2Status: selected })}
                        onNotChange={(selected: string[]) => updatePendingFilters({ notCompanyOverviewV2Status: selected })}
                        placeholder="Select status..."
                        disabled={isLoading}
                        showNotFilter={true}
                        filterKey="companyOverviewV2Status"
                      />
                    </div>
                    <div className="space-y-3">
                      <Label className="text-sm font-medium text-gray-700">Investment Criteria</Label>
                      <EnhancedMultiSelect
                        label=""
                        options={processingStatusOptions.map(option => ({ 
                          value: option.value, 
                          label: option.label 
                        }))}
                        selected={pendingFilters.companyInvestmentCriteriaStatus || []}
                        notSelected={pendingFilters.notCompanyInvestmentCriteriaStatus || []}
                        onChange={(selected: string[]) => updatePendingFilters({ companyInvestmentCriteriaStatus: selected })}
                        onNotChange={(selected: string[]) => updatePendingFilters({ notCompanyInvestmentCriteriaStatus: selected })}
                        placeholder="Select status..."
                        disabled={isLoading}
                        showNotFilter={true}
                        filterKey="companyInvestmentCriteriaStatus"
                      />
                    </div>
                  </div>
                  
                  {/* Company Investment Criteria Existence Filter */}
                  <div className="space-y-3">
                    <Label className="text-sm font-medium text-gray-700">Company Investment Criteria Data</Label>
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-2">
                        <input
                          type="radio"
                          id="company-has-ic-yes"
                          name="companyHasInvestmentCriteria"
                          checked={pendingFilters.companyHasInvestmentCriteria === true}
                          onChange={() => updatePendingFilters({ companyHasInvestmentCriteria: true })}
                          className="h-4 w-4 text-orange-600 focus:ring-orange-500"
                          disabled={isLoading}
                        />
                        <Label htmlFor="company-has-ic-yes" className="text-sm text-gray-700">
                          Has Investment Criteria
                        </Label>
                      </div>
                      <div className="flex items-center gap-2">
                        <input
                          type="radio"
                          id="company-has-ic-no"
                          name="companyHasInvestmentCriteria"
                          checked={pendingFilters.companyHasInvestmentCriteria === false}
                          onChange={() => updatePendingFilters({ companyHasInvestmentCriteria: false })}
                          className="h-4 w-4 text-orange-600 focus:ring-orange-500"
                          disabled={isLoading}
                        />
                        <Label htmlFor="company-has-ic-no" className="text-sm text-gray-700">
                          No Investment Criteria
                        </Label>
                      </div>
                      <div className="flex items-center gap-2">
                        <input
                          type="radio"
                          id="company-has-ic-all"
                          name="companyHasInvestmentCriteria"
                          checked={pendingFilters.companyHasInvestmentCriteria === undefined}
                          onChange={() => updatePendingFilters({ companyHasInvestmentCriteria: undefined })}
                          className="h-4 w-4 text-orange-600 focus:ring-orange-500"
                          disabled={isLoading}
                        />
                        <Label htmlFor="company-has-ic-all" className="text-sm text-gray-700">
                          All Companies
                        </Label>
                      </div>
                    </div>
                    <p className="text-xs text-gray-500">
                      Filter contacts based on whether their company has investment criteria data in the central table
                    </p>
                  </div>
                </CardContent>
              )}
            </Card>
            )}

            {/* Investment Criteria V2 Enhanced Filters - Admin & User */}
            {canShowSection('investment_criteria') && (
            <Card className="border-0 shadow-sm bg-gradient-to-r from-purple-50 to-pink-50">
              <CardHeader 
                className="pb-4 cursor-pointer hover:bg-purple-100/50 transition-colors rounded-t-lg" 
                onClick={() => toggleSection('investment_criteria')}
                title={`Click to ${expandedSections.investment_criteria ? 'collapse' : 'expand'} Investment Criteria V2 Enhanced filters`}
              >
                <CardTitle className="flex items-center justify-between text-lg">
                  <div className="flex items-center gap-3">
                    <Target className="h-5 w-5 text-purple-600" />
                    Investment Criteria
                    <Badge className="bg-gray-100 text-gray-600 border border-gray-200 text-xs">
                      {expandedSections.investment_criteria ? 'Expanded' : 'Click to expand'}
                    </Badge>
                  </div>
                  <ChevronRight className={`h-5 w-5 transition-transform ${expandedSections.investment_criteria ? 'rotate-90' : ''}`} />
                </CardTitle>
              </CardHeader>
              {expandedSections.investment_criteria && (
                <CardContent className="space-y-6">
                  {/* Capital Position - Always visible for User & Admin */}
                  <EnhancedMultiSelect
                    label="Capital Position"
                    options={investmentCriteriaOptions.capitalPositions}
                    selected={pendingFilters.capitalPosition || []}
                    notSelected={pendingFilters.notCapitalPosition || []}
                    onChange={(selected: string[]) => updatePendingFilters({ capitalPosition: selected })}
                    onNotChange={(selected: string[]) => updatePendingFilters({ notCapitalPosition: selected })}
                    placeholder={loadingInvestmentCriteriaOptions ? "Loading capital positions..." : "Select capital positions..."}
                    disabled={isLoading || loadingInvestmentCriteriaOptions}
                    showNotFilter={userRole === 'admin'}
                    filterKey="capitalPosition"
                  />

                  {/* Deal Size Range - Always visible for User & Admin */}
                  <div className="space-y-3">
                    <Label className="text-sm font-medium text-gray-700">Deal Size Range</Label>
                    <div className="grid grid-cols-2 gap-4">
                      <Input
                        type="number"
                        placeholder={userRole === 'guest' ? "Min (Read Only)" : "Min (whole number)"}
                        value={getRangeInputValue('dealSizeMin', pendingFilters.dealSizeMin)}
                        onChange={userRole === 'guest' ? () => {} : (e) => updateLocalRangeInput('dealSizeMin', e.target.value)}
                        onBlur={userRole === 'guest' ? () => {} : (e) => applyRangeFilter('dealSizeMin', e.target.value)}
                        onKeyDown={userRole === 'guest' ? () => {} : (e) => e.key === 'Enter' && applyRangeFilter('dealSizeMin', e.currentTarget.value)}
                        disabled={isLoading || userRole === 'guest'}
                        className="bg-white border-gray-200"
                      />
                      <Input
                        type="number"
                        placeholder={userRole === 'guest' ? "Max (Read Only)" : "Max (whole number)"}
                        value={getRangeInputValue('dealSizeMax', pendingFilters.dealSizeMax)}
                        onChange={userRole === 'guest' ? () => {} : (e) => updateLocalRangeInput('dealSizeMax', e.target.value)}
                        onBlur={userRole === 'guest' ? () => {} : (e) => applyRangeFilter('dealSizeMax', e.target.value)}
                        onKeyDown={userRole === 'guest' ? () => {} : (e) => e.key === 'Enter' && applyRangeFilter('dealSizeMax', e.currentTarget.value)}
                        disabled={isLoading || userRole === 'guest'}
                        className="bg-white border-gray-200"
                      />
                    </div>
                  </div>

                  {/* Target Return Range */}
                  <div className="space-y-3">
                    <Label className="text-sm font-medium text-gray-700">Target Return Range (%)</Label>
                    <div className="grid grid-cols-2 gap-4">
                      <Input
                        type="number"
                        placeholder="Min (%)"
                        value={getRangeInputValue('targetReturnMin', pendingFilters.targetReturnMin)}
                        onChange={(e) => updateLocalRangeInput('targetReturnMin', e.target.value)}
                        onBlur={(e) => applyRangeFilter('targetReturnMin', e.target.value)}
                        onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('targetReturnMin', e.currentTarget.value)}
                        disabled={isLoading}
                        className="bg-white border-gray-200"
                      />
                      <Input
                        type="number"
                        placeholder="Max (%)"
                        value={getRangeInputValue('targetReturnMax', pendingFilters.targetReturnMax)}
                        onChange={(e) => updateLocalRangeInput('targetReturnMax', e.target.value)}
                        onBlur={(e) => applyRangeFilter('targetReturnMax', e.target.value)}
                        onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('targetReturnMax', e.currentTarget.value)}
                        disabled={isLoading}
                        className="bg-white border-gray-200"
                      />
                    </div>
                  </div>

                  {/* Property Types - Always visible for User & Admin */}
                  <div className="space-y-3">
                    <Label className="text-sm font-medium text-gray-700">Property Types</Label>
                    <NestedMappingSelector
                      mappingType="Property Type"
                      selectedValues={pendingFilters.propertyTypes || []}
                      onSelectionChange={userRole === 'guest' ? () => {} : (values) => {
                        updatePendingFilters({ propertyTypes: values });
                      }}
                      label=""
                      placeholder={userRole === 'guest' ? "Property Types (Read Only)" : loadingInvestmentCriteriaOptions ? "Loading property types..." : "Select property types or type to add new..."}
                      showSelectAll={userRole !== 'guest'}
                      selectAllLabel="Select All Property Types"
                      mappingData={mappingData.propertyTypeRaw}
                      allowNewOptions={userRole !== 'guest'}
                      onAddNewOption={(newValue) => {
                        console.log(`Added new property type: ${newValue}`);
                      }}
                    />
                  </div>

                  {/* Strategies - Always visible for User & Admin */}
                  <EnhancedMultiSelect
                    label="Strategies"
                    options={investmentCriteriaOptions.strategies}
                    selected={pendingFilters.strategies || []}
                    notSelected={pendingFilters.notStrategies || []}
                    onChange={(selected: string[]) => updatePendingFilters({ strategies: selected })}
                    onNotChange={(selected: string[]) => updatePendingFilters({ notStrategies: selected })}
                    placeholder={loadingInvestmentCriteriaOptions ? "Loading strategies..." : "Select strategies..."}
                    disabled={isLoading || loadingInvestmentCriteriaOptions}
                    showNotFilter={userRole === 'admin'}
                    filterKey="strategies"
                  />

                  {/* Geographic Filters - Always visible for User & Admin */}
                  <div className="grid grid-cols-2 gap-4">
                    <EnhancedMultiSelect
                      label="Countries"
                      options={investmentCriteriaOptions.countries}
                      selected={pendingFilters.countries || []}
                      onChange={(selected: string[]) => updatePendingFilters({ countries: selected })}
                      placeholder={loadingInvestmentCriteriaOptions ? "Loading countries..." : "Select countries..."}
                      disabled={isLoading || loadingInvestmentCriteriaOptions}
                      showNotFilter={false}
                      filterKey="countries"
                    />

                    <div className="space-y-2">
                      <Label className="text-sm font-medium text-gray-700">U.S. Regions</Label>
                      <NestedMappingSelector
                        mappingType="U.S Regions"
                        selectedValues={pendingFilters.regions || []}
                        onSelectionChange={userRole === 'guest' ? () => {} : (values) => {
                          updatePendingFilters({ regions: values });
                        }}
                        label=""
                        placeholder={userRole === 'guest' ? "Regions (Read Only)" : loadingInvestmentCriteriaOptions ? "Loading regions..." : "Select regions or type to add new..."}
                        showSelectAll={userRole !== 'guest'}
                        selectAllLabel="Select All Regions"
                        mappingData={mappingData.regionsRaw}
                        allowNewOptions={userRole !== 'guest'}
                        onAddNewOption={(newValue) => {
                          console.log(`Added new region: ${newValue}`);
                        }}
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label className="text-sm font-medium text-gray-700">U.S. States</Label>
                      <NestedMappingSelector
                        key={`states-${(pendingFilters.regions || []).join(',')}`}
                        mappingType="U.S States"
                        selectedValues={pendingFilters.states || []}
                        onSelectionChange={userRole === 'guest' ? () => {} : (values) => {
                          updatePendingFilters({ states: values });
                        }}
                        label=""
                        placeholder={userRole === 'guest' ? "States (Read Only)" : pendingFilters.regions && pendingFilters.regions.length > 0 
                          ? `Select states for selected regions or type to add new...` 
                          : "Select states or type to add new..."}
                        showSelectAll={userRole !== 'guest'}
                        selectAllLabel="Select All States"
                        mappingData={mappingData.regionsRaw}
                        allowNewOptions={userRole !== 'guest'}
                        onAddNewOption={(newValue) => {
                          console.log(`Added new state: ${newValue}`);
                        }}
                        // Nested props
                        parentMappingData={mappingData.regionsRaw}
                        parentSelectedValues={pendingFilters.regions || []}
                        parentField="region"
                        childField="state"
                        isChild={true}
                      />
                    </div>

                    <EnhancedMultiSelect
                      label="Cities"
                      options={investmentCriteriaOptions.cities}
                      selected={pendingFilters.cities || []}
                      onChange={(selected: string[]) => updatePendingFilters({ cities: selected })}
                      placeholder={loadingInvestmentCriteriaOptions ? "Loading cities..." : "Select cities..."}
                      disabled={isLoading || loadingInvestmentCriteriaOptions}
                      showNotFilter={false}
                      filterKey="cities"
                    />
                  </div>

                  {/* Property Subcategories - Always visible for User & Admin */}
                  <div className="space-y-3">
                    <Label className="text-sm font-medium text-gray-700">Property Subcategories</Label>
                    <ReactMultiSelect
                      options={investmentCriteriaOptions.propertySubcategories}
                      selected={pendingFilters.propertySubcategories || []}
                      onChange={userRole === 'guest' ? () => {} : (selected: string[]) => updatePendingFilters({ propertySubcategories: selected })}
                      placeholder={userRole === 'guest' ? "Subcategories (Read Only)" : loadingInvestmentCriteriaOptions ? "Loading subcategories..." : "Select subcategories..."}
                      disabled={isLoading || loadingInvestmentCriteriaOptions || userRole === 'guest'}
                      showSelectAll={userRole !== 'guest'}
                      selectAllLabel="Select All Subcategories"
                    />
                  </div>

                  {/* Conditional Equity Fields - Show when Equity is selected in Capital Position */}
                  {shouldShowEquityFields() && (
                    <div className="border-t pt-4 mt-6">
                      <h4 className="font-medium text-gray-800 mb-4 text-sm bg-green-50 p-2 rounded border-l-4 border-green-400">
                        📊 Equity Investment Criteria (IRR & EM)
                      </h4>
                      
                      {/* Minimum IRR Range */}
                      <div className="space-y-3 mb-4">
                        <Label className="text-sm font-medium text-gray-700">Minimum IRR Range (%)</Label>
                        <div className="grid grid-cols-2 gap-4">
                          <Input
                            type="number"
                            step="0.01"
                            placeholder={userRole === 'guest' ? "Min IRR (Read Only)" : "Min IRR (%)"}
                            value={getRangeInputValue('minimumIrrMin', pendingFilters.minimumIrrMin)}
                            onChange={userRole === 'guest' ? () => {} : (e) => updateLocalRangeInput('minimumIrrMin', e.target.value)}
                            onBlur={userRole === 'guest' ? () => {} : (e) => applyRangeFilter('minimumIrrMin', e.target.value)}
                            onKeyDown={userRole === 'guest' ? () => {} : (e) => e.key === 'Enter' && applyRangeFilter('minimumIrrMin', e.currentTarget.value)}
                            disabled={isLoading || userRole === 'guest'}
                            className="bg-white border-gray-200"
                          />
                          <Input
                            type="number"
                            step="0.01"
                            placeholder={userRole === 'guest' ? "Max IRR (Read Only)" : "Max IRR (%)"}
                            value={getRangeInputValue('minimumIrrMax', pendingFilters.minimumIrrMax)}
                            onChange={userRole === 'guest' ? () => {} : (e) => updateLocalRangeInput('minimumIrrMax', e.target.value)}
                            onBlur={userRole === 'guest' ? () => {} : (e) => applyRangeFilter('minimumIrrMax', e.target.value)}
                            onKeyDown={userRole === 'guest' ? () => {} : (e) => e.key === 'Enter' && applyRangeFilter('minimumIrrMax', e.currentTarget.value)}
                            disabled={isLoading || userRole === 'guest'}
                            className="bg-white border-gray-200"
                          />
                        </div>
                      </div>

                      {/* Equity Multiplier (EM) - Target Cash on Cash Range */}
                      <div className="space-y-3">
                        <Label className="text-sm font-medium text-gray-700">Equity Multiplier (EM) - Target Cash on Cash Range (%)</Label>
                        <div className="grid grid-cols-2 gap-4">
                          <Input
                            type="number"
                            step="0.01"
                            placeholder={userRole === 'guest' ? "Min EM (Read Only)" : "Min Cash on Cash (%)"}
                            value={getRangeInputValue('targetCashOnCashMin', pendingFilters.targetCashOnCashMin)}
                            onChange={userRole === 'guest' ? () => {} : (e) => updateLocalRangeInput('targetCashOnCashMin', e.target.value)}
                            onBlur={userRole === 'guest' ? () => {} : (e) => applyRangeFilter('targetCashOnCashMin', e.target.value)}
                            onKeyDown={userRole === 'guest' ? () => {} : (e) => e.key === 'Enter' && applyRangeFilter('targetCashOnCashMin', e.currentTarget.value)}
                            disabled={isLoading || userRole === 'guest'}
                            className="bg-white border-gray-200"
                          />
                          <Input
                            type="number"
                            step="0.01"
                            placeholder={userRole === 'guest' ? "Max EM (Read Only)" : "Max Cash on Cash (%)"}
                            value={getRangeInputValue('targetCashOnCashMax', pendingFilters.targetCashOnCashMax)}
                            onChange={userRole === 'guest' ? () => {} : (e) => updateLocalRangeInput('targetCashOnCashMax', e.target.value)}
                            onBlur={userRole === 'guest' ? () => {} : (e) => applyRangeFilter('targetCashOnCashMax', e.target.value)}
                            onKeyDown={userRole === 'guest' ? () => {} : (e) => e.key === 'Enter' && applyRangeFilter('targetCashOnCashMax', e.currentTarget.value)}
                            disabled={isLoading || userRole === 'guest'}
                            className="bg-white border-gray-200"
                          />
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Conditional Debt Fields - Show when Debt positions are selected in Capital Position */}
                  {shouldShowDebtFields() && (
                    <div className="border-t pt-4 mt-6">
                      <h4 className="font-medium text-gray-800 mb-4 text-sm bg-amber-50 p-2 rounded border-l-4 border-amber-400">
                        💰 Debt Investment Criteria
                      </h4>
                      
                      {/* Loan Types */}
                      <div className="space-y-3 mb-4">
                        <Label className="text-sm font-medium text-gray-700">Loan Types</Label>
                        <ReactMultiSelect
                          options={investmentCriteriaOptions.loanTypes || []}
                          selected={pendingFilters.loanTypes || []}
                          onChange={userRole === 'guest' ? () => {} : (selected: string[]) => updatePendingFilters({ loanTypes: selected })}
                          placeholder={userRole === 'guest' ? "Loan Types (Read Only)" : loadingInvestmentCriteriaOptions ? "Loading loan types..." : "Select loan types..."}
                          disabled={isLoading || loadingInvestmentCriteriaOptions || userRole === 'guest'}
                          showSelectAll={userRole !== 'guest'}
                          selectAllLabel="Select All Loan Types"
                        />
                      </div>

                      {/* Loan Interest Rate SOFR Range */}
                      <div className="space-y-3">
                        <Label className="text-sm font-medium text-gray-700">Interest Rate (SOFR) Range (%)</Label>
                        <div className="grid grid-cols-2 gap-4">
                          <Input
                            type="number"
                            step="0.01"
                            placeholder={userRole === 'guest' ? "Min Rate (Read Only)" : "Min rate (%)"}
                            value={getRangeInputValue('loanInterestRateSofrMin', pendingFilters.loanInterestRateSofrMin)}
                            onChange={userRole === 'guest' ? () => {} : (e) => updateLocalRangeInput('loanInterestRateSofrMin', e.target.value)}
                            onBlur={userRole === 'guest' ? () => {} : (e) => applyRangeFilter('loanInterestRateSofrMin', e.target.value)}
                            onKeyDown={userRole === 'guest' ? () => {} : (e) => e.key === 'Enter' && applyRangeFilter('loanInterestRateSofrMin', e.currentTarget.value)}
                            disabled={isLoading || userRole === 'guest'}
                            className="bg-white border-gray-200"
                          />
                          <Input
                            type="number"
                            step="0.01"
                            placeholder={userRole === 'guest' ? "Max Rate (Read Only)" : "Max rate (%)"}
                            value={getRangeInputValue('loanInterestRateSofrMax', pendingFilters.loanInterestRateSofrMax)}
                            onChange={userRole === 'guest' ? () => {} : (e) => updateLocalRangeInput('loanInterestRateSofrMax', e.target.value)}
                            onBlur={userRole === 'guest' ? () => {} : (e) => applyRangeFilter('loanInterestRateSofrMax', e.target.value)}
                            onKeyDown={userRole === 'guest' ? () => {} : (e) => e.key === 'Enter' && applyRangeFilter('loanInterestRateSofrMax', e.currentTarget.value)}
                            disabled={isLoading || userRole === 'guest'}
                            className="bg-white border-gray-200"
                          />
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Decision Making Process - Admin Only */}
                  {userRole === 'admin' && (
                    <div className="space-y-3">
                      <Label className="text-sm font-medium text-gray-700">Decision Making Process</Label>
                      <ReactMultiSelect
                        options={investmentCriteriaOptions.decisionMakingProcesses || []}
                        selected={pendingFilters.decisionMakingProcess || []}
                        onChange={(selected: string[]) => updatePendingFilters({ decisionMakingProcess: selected })}
                        placeholder={loadingInvestmentCriteriaOptions ? "Loading decision processes..." : "Select decision processes..."}
                        disabled={isLoading || loadingInvestmentCriteriaOptions}
                      />
                    </div>
                  )}



                </CardContent>
              )}
            </Card>
            )}

            {/* Gmail Outreach Table Filters - Admin Only */}
            {canShowSection('gmail_outreach') && (
            <Card className="border-0 shadow-sm bg-gradient-to-r from-pink-50 to-rose-50">
              <CardHeader 
                className="pb-4 cursor-pointer hover:bg-pink-100/50 transition-colors rounded-t-lg" 
                onClick={() => toggleSection('gmail_outreach')}
                title={`Click to ${expandedSections.gmail_outreach ? 'collapse' : 'expand'} Gmail Outreach filters`}
              >
                <CardTitle className="flex items-center justify-between text-lg">
                  <div className="flex items-center gap-3">
                    <Send className="h-5 w-5 text-pink-600" />
                    Gmail Outreach Table
                    <Badge className="bg-pink-100 text-pink-700 border border-pink-200">
                      Email Tracking
                    </Badge>
                    <Badge className="bg-gray-100 text-gray-600 border border-gray-200 text-xs">
                      {expandedSections.gmail_outreach ? 'Expanded' : 'Click to expand'}
                    </Badge>
                  </div>
                  <ChevronRight className={`h-5 w-5 transition-transform ${expandedSections.gmail_outreach ? 'rotate-90' : ''}`} />
                </CardTitle>
              </CardHeader>
              {expandedSections.gmail_outreach && (
                <CardContent className="space-y-6">
                  {/* Has Been Reached Out */}
                  <div className="space-y-3">
                    <Label className="text-sm font-medium text-gray-700">Email Outreach Status</Label>
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-2">
                        <input
                          type="radio"
                          id="reached-out-yes"
                          name="hasBeenReachedOut"
                          checked={pendingFilters.hasBeenReachedOut === true}
                          onChange={() => updatePendingFilters({ hasBeenReachedOut: true })}
                          className="h-4 w-4 text-pink-600 focus:ring-pink-500"
                          disabled={isLoading}
                        />
                        <Label htmlFor="reached-out-yes" className="text-sm text-gray-700">
                          Already Reached Out
                        </Label>
                      </div>
                      <div className="flex items-center gap-2">
                        <input
                          type="radio"
                          id="reached-out-no"
                          name="hasBeenReachedOut"
                          checked={pendingFilters.hasBeenReachedOut === false}
                          onChange={() => updatePendingFilters({ hasBeenReachedOut: false })}
                          className="h-4 w-4 text-pink-600 focus:ring-pink-500"
                          disabled={isLoading}
                        />
                        <Label htmlFor="reached-out-no" className="text-sm text-gray-700">
                          Not Yet Reached Out
                        </Label>
                      </div>
                      <div className="flex items-center gap-2">
                        <input
                          type="radio"
                          id="reached-out-all"
                          name="hasBeenReachedOut"
                          checked={pendingFilters.hasBeenReachedOut === undefined}
                          onChange={() => updatePendingFilters({ hasBeenReachedOut: undefined })}
                          className="h-4 w-4 text-pink-600 focus:ring-pink-500"
                          disabled={isLoading}
                        />
                        <Label htmlFor="reached-out-all" className="text-sm text-gray-700">
                          All Contacts
                        </Label>
                      </div>
                    </div>
                  </div>
                </CardContent>
              )}
            </Card>
            )}

          </div>

          {/* Enhanced Panel Footer V2 */}
          <div className="border-t border-gray-200 p-6 bg-gradient-to-r from-gray-50 to-purple-50">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-600">
                {activeFilterCount > 0 ? (
                  <div className="flex items-center gap-2">
                    <Database className="h-4 w-4 text-purple-600" />
                    <span className="font-medium text-gray-700">
                      {activeFilterCount} Advanced Filter{activeFilterCount !== 1 ? 's' : ''} Active
                    </span>
                    <Badge className="bg-amber-100 text-amber-800 border border-amber-200 font-semibold">
                      V2 Enhanced Query
                    </Badge>
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    <Filter className="h-4 w-4 text-gray-400" />
                    <span>No filters applied - showing all contact data</span>
                  </div>
                )}
              </div>
              
              <div className="flex items-center gap-3">
                {/* Quick Reset */}
                {activeFilterCount > 0 && (
                  <Button
                    onClick={handleClearFilters}
                    className="text-gray-600 hover:text-red-600 bg-white border border-gray-200 hover:border-red-200 hover:bg-red-50 px-4 py-2 text-sm transition-colors"
                  >
                    <RotateCcw className="h-4 w-4 mr-2" />
                    Reset All
                  </Button>
                )}
                
                {/* Apply Button */}
                <Button
                  onClick={activeFilterCount > 0 ? applyFilters : () => setIsFilterPanelOpen(false)}
                  className={`px-6 py-2 font-medium shadow-md hover:shadow-lg transition-all duration-200 ${
                    activeFilterCount > 0 
                      ? 'bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white' 
                      : 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white'
                  }`}
                >
                  {activeFilterCount > 0 ? (
                    <>
                      <Database className="h-4 w-4 mr-2" />
                      Apply {activeFilterCount} V2 Filter{activeFilterCount !== 1 ? 's' : ''}
                    </>
                  ) : (
                    <>
                      <ChevronRight className="h-4 w-4 mr-2" />
                      Close Panel
                    </>
                  )}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
