import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON>H<PERSON>er, Card<PERSON><PERSON>le, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { FileUpload } from "@/components/ui/file-upload";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Upload,
  CheckCircle,
  AlertCircle,
  Database,
  FileText,
  User,
  ArrowLeft,
  Search,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { ContactMultiSelect } from "@/components/ui/contact-multi-select";
import { useRouter } from "next/navigation";
import { DealConflictModal } from "@/components/conflicts/DealConflictModal";
import { JobConflictModal } from "@/components/conflicts/JobConflictModal";
import { type PotentialDealDuplicate } from "@/lib/utils/dealConflictDetector";

interface Contact {
  contact_id: number;
  first_name: string | null;
  last_name: string | null;
  email: string | null;
  personal_email: string | null;
  company: string | null;
  title: string | null;
}

interface SimplifiedDealUploadProps {
  showBackButton?: boolean;
}

const SimplifiedDealUpload: React.FC<SimplifiedDealUploadProps> = ({
  showBackButton = true,
}) => {
  const router = useRouter();
  const [files, setFiles] = useState<File[]>([]);
  const [selectedContacts, setSelectedContacts] = useState<Contact[]>([]);
  const [uploading, setUploading] = useState(false);
  const [message, setMessage] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [dbStatus, setDbStatus] = useState<{
    saved: boolean;
    error?: string;
  } | null>(null);
  const [result, setResult] = useState<any>(null);
  const [duplicates, setDuplicates] = useState<PotentialDealDuplicate[]>([]);
  const [showConflictModal, setShowConflictModal] = useState(false);
  const [showJobConflictModal, setShowJobConflictModal] = useState(false);
  const [lastUploadData, setLastUploadData] = useState<any>(null);
  const [jobConflictData, setJobConflictData] = useState<{
    jobId: string;
    duplicates: PotentialDealDuplicate[];
    extractedData: any;
  } | null>(null);
  const [isResolvingJobConflict, setIsResolvingJobConflict] = useState(false);
  // Default to gemini-flash and v2 processor
  const selectedModel = "gemini-flash";
  const selectedProcessor = "v2";

  const handleBackToDeals = () => {
    router.push("/dashboard/deals");
  };



  // Check job status and handle conflicts
  const checkJobStatus = async (jobId: string) => {
    try {
      const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || '';
      const response = await fetch(`${baseUrl}/api/deals/job-conflicts?jobId=${jobId}`);
      const data = await response.json();

      if (response.ok && data.success) {
        const job = data.jobData;
        const jobState = data.jobState;

        // Check if job has completed with conflicts
        if (jobState === "completed" && job.returnvalue?.duplicates) {
          setJobConflictData({
            jobId,
            duplicates: job.returnvalue.duplicates,
            extractedData: job.returnvalue.extractedData,
          });
          setShowJobConflictModal(true);
          return true;
        }

        // If job is still processing, check again in a few seconds
        if (jobState === "active" || jobState === "waiting") {
          setTimeout(() => checkJobStatus(jobId), 3000);
          return false;
        }

        // If job completed successfully
        if (jobState === "completed" && job.returnvalue?.success) {
          setMessage(`Deal processed successfully! Deal ID: ${job.returnvalue.dealId}`);
          return true;
        }

        // If job failed
        if (jobState === "failed") {
          setError("Job processing failed. Please try again.");
          return true;
        }
      }
    } catch (error) {
      console.error("Error checking job status:", error);
    }
    return false;
  };

  const handleUpload = async () => {
    if (files.length === 0) return;
    setUploading(true);
    setMessage(null);
    setError(null);
    setDbStatus(null);
    setResult(null);

    try {
      const formData = new FormData();

      // Add all files with descriptive names
      files.forEach((file, index) => {
        const fileType = getFileType(file.name);
        formData.append(`file_${index}`, file);
        formData.append(`file_type_${index}`, fileType);
      });

      // Add selected contacts if provided
      if (selectedContacts.length > 0) {
        const contactEmails = selectedContacts.map(contact => 
          contact.email || contact.personal_email
        ).filter(Boolean);
        formData.append("contact_emails", JSON.stringify(contactEmails));
      }

      // Add created_by for job tracking
      formData.append("created_by", "upload_ui");

      // Add selected model
      formData.append("llm_model", selectedModel);

      // Add selected processor
      formData.append("processor_version", selectedProcessor);

      // Debug logging to track model selection
      console.log("🔍 Frontend - Model Selection Debug:");
      console.log("  - Selected model being sent:", selectedModel);
      console.log("  - Selected processor being sent:", selectedProcessor);

      // Choose API endpoint based on processor version
      const apiEndpoint = selectedProcessor === "v2" 
        ? "/api/v2/deals/upload" 
        : "/api/deals/upload-simplified";

      // Use API_BASE_URL if available, otherwise use relative path
      const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || '';
      const fullApiUrl = `${baseUrl}${apiEndpoint}`;

      const res = await fetch(fullApiUrl, {
        method: "POST",
        body: formData,
      });
      const data = await res.json();

      if (res.ok && data.success) {
        // Handle both job-based (v1) and direct (v2) responses
        if (data.jobId) {
          // V1 job-based response
          const jobMessage = `${data.message} (Job ID: ${data.jobId})`;
          setMessage(jobMessage);
          setResult({
            jobId: data.jobId,
            fileCount: data.fileCount,
            fileNames: data.fileNames,
            estimatedProcessingTime: data.estimatedProcessingTime,
            isJobBased: true,
          });
          setDbStatus({ saved: true });

          // Start checking job status for conflicts
          setTimeout(() => checkJobStatus(data.jobId), 2000);
        } else {
          // V2 direct response
          setMessage(data.message);
          setResult({
            fileCount: data.fileCount,
            fileNames: data.fileNames,
            isJobBased: false,
            processor: selectedProcessor,
          });
          setDbStatus({ saved: true });
        }

        // Clear files after successful upload
        setFiles([]);
        setSelectedContacts([]);
        // Keep the selected model for convenience - don't reset it
      } else if (res.status === 409 && data.duplicates) {
        // Handle duplicates (legacy functionality)
        setDuplicates(data.duplicates || []);
        setLastUploadData(data.extractedData);
        setShowConflictModal(true);
        setError(null);
      } else {
        setError(data.error || "Upload failed.");
      }
    } catch (err: any) {
      setError(err.message || "Upload failed.");
    } finally {
      setUploading(false);
    }
  };

  const handleConflictResolve = (
    action: "replace" | "create_new",
    targetDealId?: number
  ) => {
    const handleResolve = async () => {
      try {
        const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || '';
        const response = await fetch(`${baseUrl}/api/deals/resolve-conflicts`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            action,
            dealData: lastUploadData?.coreFields || lastUploadData,
            targetDealId,
          }),
        });

        const result = await response.json();

        if (response.ok && result.success) {
          setShowConflictModal(false);
          setDuplicates([]);
          setLastUploadData(null);

          if (action === "replace") {
            setMessage(
              `New deal created and old deal replaced successfully (New ID: ${result.dealId})`
            );
          } else if (action === "create_new") {
            setMessage(`New deal created successfully (ID: ${result.dealId})`);
          }

          setDbStatus({ saved: true });
        } else {
          setError(result.error || "Failed to resolve duplicates");
        }
      } catch (error) {
        console.error("Error resolving duplicates:", error);
        setError("Failed to resolve duplicates");
      }
    };

    handleResolve();
  };

  const handleJobConflictResolve = async (
    action: "replace" | "create_new",
    targetDealId?: number
  ) => {
    if (!jobConflictData) return;

    setIsResolvingJobConflict(true);
    try {
      const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || '';
      const response = await fetch(`${baseUrl}/api/deals/job-conflicts`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          jobId: jobConflictData.jobId,
          action,
          targetDealId,
          extractedData: jobConflictData.extractedData,
        }),
      });

      const result = await response.json();

      if (response.ok && result.success) {
        setShowJobConflictModal(false);
        setJobConflictData(null);

        if (action === "replace") {
          setMessage(
            `Deal updated successfully! Deal ID: ${result.dealId}`
          );
        } else if (action === "create_new") {
          setMessage(`New deal created successfully! Deal ID: ${result.dealId}`);
        }

        setDbStatus({ saved: true });
      } else {
        setError(result.error || "Failed to resolve conflicts");
      }
    } catch (error) {
      console.error("Error resolving job conflicts:", error);
      setError("Failed to resolve conflicts");
    } finally {
      setIsResolvingJobConflict(false);
    }
  };

  const getFileType = (fileName: string): string => {
    const name = fileName.toLowerCase();
    if (name.includes("memorandum") || name.includes("mem"))
      return "memorandum";
    if (name.includes("underwriting") || name.includes("uw"))
      return "underwriting";
    if (name.includes("term") || name.includes("sheet")) return "term_sheet";
    if (name.includes("proforma") || name.includes("model")) return "proforma";
    if (name.includes("budget") || name.includes("construction"))
      return "budget";
    if (name.includes("analysis") || name.includes("market")) return "analysis";
    return "document";
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Back Button */}
      {showBackButton && (
        <div className="flex justify-start">
          <Button
            variant="ghost"
            onClick={handleBackToDeals}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Deals
          </Button>
        </div>
      )}

      {/* Header */}
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold text-gray-900">
          Upload Deal Documents
        </h1>
        <p className="text-gray-600">
          Upload memorandum, underwriting, term sheets, pro forma models, and
          other deal documents
        </p>
      </div>

      {/* Upload Card */}
      <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
        <CardHeader className="pb-4">
          <CardTitle className="text-xl flex items-center gap-2">
            <Upload className="h-6 w-6 text-blue-600" />
            Upload Deal Documents
          </CardTitle>
          <p className="text-gray-600">
            Upload memorandum, underwriting, term sheets, pro forma models, and
            other deal documents. The system will extract data into the new
            simplified deals table structure.
          </p>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Contact Multi-Select */}
          <div className="space-y-2">
            <Label htmlFor="contact_search" className="flex items-center gap-2">
              <User className="h-4 w-4 text-gray-600" />
              Contacts (Optional)
            </Label>
            <ContactMultiSelect
              selectedContacts={selectedContacts}
              onContactsChange={setSelectedContacts}
              placeholder="Search for contacts by email..."
              className="max-w-xs"
              mode="standalone"
            />
            <p className="text-sm text-gray-500">
              Search for contacts by email to link this deal. You can select multiple contacts.
            </p>
            
            <div className="flex items-center gap-2 pt-2">
              <div className="flex-1 border-t border-gray-200"></div>
              <span className="text-sm text-gray-500 px-2">or</span>
              <div className="flex-1 border-t border-gray-200"></div>
            </div>
            
            <div className="flex items-center justify-center">
              {/* ContactCreationModal is now handled inside ContactMultiSelect */}
            </div>
          </div>


          <FileUpload
            value={files}
            onFilesSelected={setFiles}
            acceptedFileTypes={[
              ".pdf",
              ".csv",
              ".xlsx",
              ".xls",
              ".doc",
              ".docx",
            ]}
            maxFiles={10}
            maxFileSize={30 * 1024 * 1024} // 30MB
            placeholder="Drop your deal documents here"
            description="Upload memorandum, underwriting, term sheets, pro forma models, and other deal documents. Multiple files supported."
            disabled={uploading}
          />

          <Button
            className="w-full"
            onClick={handleUpload}
            disabled={!files.length || uploading}
            size="lg"
          >
            {uploading ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                Processing Documents...
              </>
            ) : (
              <>
                <Upload className="h-5 w-5 mr-2" />
                Process {files.length} Document{files.length !== 1 ? "s" : ""}
              </>
            )}
          </Button>

          {message && (
            <div className="flex items-center gap-2 p-3 bg-green-50 border border-green-200 rounded-lg">
              <CheckCircle className="h-5 w-5 text-green-500" />
              <div>
                <p className="text-green-700 font-medium">{message}</p>
                {dbStatus && dbStatus.saved && (
                  <div className="mt-1">
                    {result?.isJobBased ? (
                      <span className="text-sm text-green-600">
                        ✓ Job queued successfully - processing will begin shortly
                      </span>
                    ) : (
                      <span className="text-sm text-green-600">
                        ✓ Files processed successfully
                      </span>
                    )}
                  </div>
                )}
              </div>
            </div>
          )}

          {error && (
            <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg">
              <AlertCircle className="h-5 w-5 text-red-500" />
              <p className="text-red-700">{error}</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Results Display */}
      {result && result.isJobBased && (
        <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              Upload Successful - Processing Started
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                <div className="flex items-center gap-2 mb-2">
                  <FileText className="h-4 w-4 text-blue-600" />
                  <span className="font-medium text-blue-900">
                    Files Queued
                  </span>
                </div>
                <p className="text-2xl font-bold text-blue-700">
                  {result.fileCount}
                </p>
              </div>

              <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                <div className="flex items-center gap-2 mb-2">
                  <Database className="h-4 w-4 text-green-600" />
                  <span className="font-medium text-green-900">Job Status</span>
                </div>
                <p className="text-lg font-semibold text-green-700">
                  Processing
                </p>
              </div>

              <div className="bg-purple-50 p-4 rounded-lg border border-purple-200">
                <div className="flex items-center gap-2 mb-2">
                  <Upload className="h-4 w-4 text-purple-600" />
                  <span className="font-medium text-purple-900">ETA</span>
                </div>
                <p className="text-lg font-semibold text-purple-700">
                  ~{result.estimatedProcessingTime}s
                </p>
              </div>
            </div>

            <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
              <h4 className="font-medium text-blue-900 mb-2">
                Job ID: {result.jobId}
              </h4>
              <p className="text-sm text-blue-700 mb-2">
                Your files are being processed using{" "}
                <strong>
                  {selectedModel === "gemini-flash"
                    ? "Google Gemini 2.5 Flash"
                    : "Google Gemini 2.5 Pro"}
                </strong>
                . Check the "Recent Uploads" table below for real-time progress
                updates.
              </p>
              <div className="space-y-1">
                <p className="text-sm font-medium text-blue-900">Files:</p>
                <ul className="text-sm text-blue-700 space-y-1">
                  {result.fileNames?.map((fileName: string, idx: number) => (
                    <li key={idx} className="flex items-center gap-2">
                      <FileText className="h-3 w-3" />
                      {fileName}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Deal Conflict Modal */}
      <DealConflictModal
        isOpen={showConflictModal}
        onClose={() => setShowConflictModal(false)}
        duplicates={duplicates}
        newDealData={lastUploadData?.coreFields || lastUploadData || {}}
        onResolve={handleConflictResolve}
      />

      {/* Job Conflict Modal */}
      {jobConflictData && (
        <JobConflictModal
          isOpen={showJobConflictModal}
          onClose={() => setShowJobConflictModal(false)}
          duplicates={jobConflictData.duplicates}
          newDealData={jobConflictData.extractedData}
          jobId={jobConflictData.jobId}
          onResolve={handleJobConflictResolve}
          isResolving={isResolvingJobConflict}
        />
      )}
    </div>
  );
};

export default SimplifiedDealUpload;
