"use client";

import { useState, useEffect, useCallback } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { InvestmentCriteriaFilters } from "@/types/investment-criteria";
import { ReactMultiSelect } from "@/components/ui/react-multi-select";
import {
  Search,
  SlidersHorizontal,
  X,
  Filter,
  Target,
  DollarSign,
  MapPin,
  Calculator,
  Building,
  BarChart3,
  Building2,
  Activity,
  Clock,
  Calendar,
  TrendingUp,
  Banknote,
  Percent,
  Timer,
  LineChart,
  PieChart,
  User,
  Factory,
  Sparkles,
  ArrowUpDown,
  ChevronUp,
  ChevronDown,
  Settings,
  Globe,
  Home,
  FileText,
  Users,
  Briefcase,
  CheckCircle,
  AlertCircle,
  Star,
} from "lucide-react";

interface MappingsData {
  [key: string]: {
    parents?: string[];
    children?: string[];
    flat?: string[];
    hierarchical?: {
      [parent: string]: string[];
    };
  };
}

interface DealUnifiedFilters extends InvestmentCriteriaFilters {
  // Search fields
  searchTerm?: string;
  
  // Investment Criteria fields
  capitalPosition?: string[];
  amount?: number;
  state?: string[];
  city?: string[];
  propertyType?: string[];
  strategy?: string[];
  
  // Deal specific fields - merged into searchTerm
  matchType?: string[];
  status?: string[];
  dealStage?: string[];
  priority?: string[];
  reviewStatus?: string[];
  extractionConfidence?: string[];

  // Deal document fields
  documentType?: string[];
  documentSource?: string[];
  extractionMethod?: string[];

  // Deal financial fields
  yieldOnCostMin?: number;
  yieldOnCostMax?: number;
  projectedGpIrrMin?: number;
  projectedGpIrrMax?: number;
  projectedLpIrrMin?: number;
  projectedLpIrrMax?: number;
  projectedGpEmMin?: number;
  projectedGpEmMax?: number;
  projectedLpEmMin?: number;
  projectedLpEmMax?: number;
  projectedTotalIrrMin?: number;
  projectedTotalIrrMax?: number;
  projectedTotalEmMin?: number;
  projectedTotalEmMax?: number;

  // Deal property fields
  zipCode?: string;
  neighborhood?: string;
  lotAreaMin?: number;
  lotAreaMax?: number;
  floorAreaRatioMin?: number;
  floorAreaRatioMax?: number;
  zoningSqFtMin?: number;
  zoningSqFtMax?: number;

  // Deal metadata
  processingDurationMin?: number;
  processingDurationMax?: number;
  documentSizeBytesMin?: number;
  documentSizeBytesMax?: number;
  published?: boolean;

  // Additional filters
  markets?: string[];
  minAmount?: number;
  maxAmount?: number;

  // Date ranges
  extractionTimestampFrom?: string;
  extractionTimestampTo?: string;
  reviewedAtFrom?: string;
  reviewedAtTo?: string;
}

interface DealFiltersProps {
  filters: DealUnifiedFilters;
  mappings?: MappingsData;
  onFiltersChange: (filters: DealUnifiedFilters) => void;
  onClearFilters: () => void;
  isLoading?: boolean;
}

// Map filter group names to icons
const getGroupIcon = (groupName: string) => {
  switch (groupName) {
    case "Investment Focus":
      return Target;
    case "Deal Economics":
      return DollarSign;
    case "Geographic Focus":
      return MapPin;
    case "Loan Structure":
      return Calculator;
    case "Deal Profile":
      return Building;
    case "Deal Financials":
      return BarChart3;
    case "Deal Properties":
      return Building2;
    case "Deal Processing":
      return Activity;
    case "Other":
      return Settings;
    default:
      return Filter;
  }
};

export default function DealFiltersComponent({
  filters,
  mappings,
  onFiltersChange,
  onClearFilters,
  isLoading = false,
}: DealFiltersProps) {
  const [isFilterPanelOpen, setIsFilterPanelOpen] = useState(false);
  const [localFilters, setLocalFilters] = useState<DealUnifiedFilters>(filters);
  const [pendingFilters, setPendingFilters] = useState<DealUnifiedFilters>(filters);
  const [localRangeInputs, setLocalRangeInputs] = useState<{
    [key: string]: string;
  }>({});
  const [localTextInputs, setLocalTextInputs] = useState<{
    [key: string]: string;
  }>({});
  
  // Removed investment criteria options - focusing on deal-specific filters only
  
  // Deal-specific options
  const [dealStages, setDealStages] = useState<Array<{value: string, label: string}>>([]);
  const [dealStatuses, setDealStatuses] = useState<Array<{value: string, label: string}>>([]);
  const [states, setStates] = useState<Array<{value: string, label: string}>>([]);
  const [cities, setCities] = useState<Array<{value: string, label: string}>>([]);
  const [propertyTypes, setPropertyTypes] = useState<Array<{value: string, label: string}>>([]);
  const [strategies, setStrategies] = useState<Array<{value: string, label: string}>>([]);
  const [capitalPositions, setCapitalPositions] = useState<Array<{value: string, label: string}>>([]);
  const [markets, setMarkets] = useState<Array<{value: string, label: string}>>([]);
  const [loadingDealOptions, setLoadingDealOptions] = useState(false);
  
  // Input state (what user sees in the fields)
  const [inputSearchTerm, setInputSearchTerm] = useState<string>("");

  // Debounced search functionality (for API calls)
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState<string>("");

  // Initialize input and debounced values from filters only once on mount
  useEffect(() => {
    const searchTerm = filters.searchTerm || "";
    
    // Set both input and debounced values
    setInputSearchTerm(searchTerm);
    setDebouncedSearchTerm(searchTerm);
  }, []); // Only run once on mount

  // Removed investment criteria fetching - focusing on deal-specific filters only

  // Fetch deal-specific options
  useEffect(() => {
    async function fetchDealOptions() {
      setLoadingDealOptions(true);
      try {
        // Fetch deal stages, deal statuses, states, property types, strategies, and markets in parallel
        const [
          dealStagesResponse, 
          dealStatusesResponse, 
          statesResponse,
          propertyTypesResponse,
          strategiesResponse,
          capitalPositionsResponse,
          marketsResponse
        ] = await Promise.all([
          fetch('/api/v2/deals/deal-stages'),
          fetch('/api/v2/deals/deal-statuses'),
          fetch('/api/v2/deals/states'),
          fetch('/api/v2/deals/property-types'),
          fetch('/api/v2/deals/strategies'),
          fetch('/api/v2/deals/capital-positions'),
          fetch('/api/v2/deals/markets')
        ]);

        if (dealStagesResponse.ok) {
          const dealStagesData = await dealStagesResponse.json();
          setDealStages(dealStagesData);
        }

        if (dealStatusesResponse.ok) {
          const dealStatusesData = await dealStatusesResponse.json();
          setDealStatuses(dealStatusesData);
        }

        if (statesResponse.ok) {
          const statesData = await statesResponse.json();
          setStates(statesData);
        }

        if (propertyTypesResponse.ok) {
          const propertyTypesData = await propertyTypesResponse.json();
          setPropertyTypes(propertyTypesData);
        }

        if (strategiesResponse.ok) {
          const strategiesData = await strategiesResponse.json();
          setStrategies(strategiesData);
        }

        if (capitalPositionsResponse.ok) {
          const capitalPositionsData = await capitalPositionsResponse.json();
          setCapitalPositions(capitalPositionsData);
        }

        if (marketsResponse.ok) {
          const marketsData = await marketsResponse.json();
          setMarkets(marketsData);
        }
      } catch (error) {
        console.error('Error fetching deal options:', error);
      } finally {
        setLoadingDealOptions(false);
      }
    }

    fetchDealOptions();
  }, []);

  // Fetch cities when states change
  const fetchCities = async (selectedStates: string[]) => {
    if (selectedStates.length === 0) {
      setCities([]);
      return;
    }
    
    try {
      const statesParam = selectedStates.join(',');
      const response = await fetch(`/api/v2/deals/cities?states=${encodeURIComponent(statesParam)}`);
      if (response.ok) {
        const citiesData = await response.json();
        setCities(citiesData);
      }
    } catch (error) {
      console.error('Error fetching cities:', error);
    }
  };

  // Fetch cities when states selection changes
  useEffect(() => {
    if (pendingFilters.state && pendingFilters.state.length > 0) {
      fetchCities(pendingFilters.state);
    } else {
      setCities([]);
    }
  }, [pendingFilters.state]);

  // Sync local filters with prop changes (prevent unnecessary resets)
  useEffect(() => {
    console.log("=== FILTERS PROP CHANGED ===");
    console.log("New filters prop:", filters);
    console.log("Current local filters:", localFilters);
    console.log("Current pending filters:", pendingFilters);
    console.log("============================");
    setLocalFilters(filters);
    setPendingFilters(filters);
    
    // Reset input states when filters are cleared
    setInputSearchTerm(filters.searchTerm || "");
    setDebouncedSearchTerm(filters.searchTerm || "");
  }, [filters]);

  // Update parent when local filters change
  const updateFilters = useCallback((newFilters: Partial<DealUnifiedFilters>) => {
    console.log("=== updateFilters CALLED ===");
    console.log("Local filters:", localFilters);
    console.log("New filters:", newFilters);
    console.log("Call stack:", new Error().stack);
    console.log("=============================");
    
    // Don't reset page for pagination-related changes
    const isPaginationChange = Object.keys(newFilters).every(key => 
      ['page', 'limit', 'sortBy', 'sortOrder'].includes(key)
    );
    
    const updatedFilters = { 
      ...localFilters, 
      ...newFilters, 
      page: isPaginationChange ? localFilters.page : 1 
    };
    console.log(localFilters,';hh',newFilters)
    setLocalFilters(updatedFilters);
    
    // Defer the parent notification to avoid setState during render
    setTimeout(() => {
      onFiltersChange(updatedFilters);
    }, 0);
  }, [localFilters, onFiltersChange]);

  // Update pending filters (not applied yet)
  const updatePendingFilters = useCallback((newFilters: Partial<DealUnifiedFilters>) => {
    console.log("=== updatePendingFilters CALLED ===");
    console.log("New filters:", newFilters);
    
    setPendingFilters(prevPendingFilters => {
      const updatedPendingFilters = {
        ...prevPendingFilters,
        ...newFilters,
        page: 1, // Reset to first page when filters change
      };
      
      console.log("Previous pending filters:", prevPendingFilters);
      console.log("Updated pending filters:", updatedPendingFilters);
      console.log("================================");
      
      return updatedPendingFilters;
    });
  }, []);

  // Apply filters when user clicks Apply button
  const applyFilters = useCallback(() => {
    console.log("=== applyFilters CALLED ===");
    
    setPendingFilters(currentPendingFilters => {
      console.log("Applying pending filters:", currentPendingFilters);
      
      setLocalFilters(currentPendingFilters);
      setIsFilterPanelOpen(false);
      
      // Defer the parent notification to avoid setState during render
      setTimeout(() => {
        onFiltersChange(currentPendingFilters);
      }, 0);
      
      return currentPendingFilters;
    });
  }, [onFiltersChange]);


    // Debounced search effect
  useEffect(() => {
    const timer = setTimeout(() => {
      // Only trigger if the search term actually changed and is meaningful
      const newSearchTerm = debouncedSearchTerm.trim() === "" ? undefined : debouncedSearchTerm;
      const currentSearchTerm = localFilters.searchTerm || undefined;
      
      if (newSearchTerm !== currentSearchTerm) {
        console.log("=== DEBOUNCED SEARCH TRIGGERED ===");
        console.log("debouncedSearchTerm:", debouncedSearchTerm);
        console.log("newSearchTerm:", newSearchTerm);
        console.log("localFilters.searchTerm:", localFilters.searchTerm);
        console.log("currentSearchTerm:", currentSearchTerm);
        console.log("================================");
        const updatedFilters = { ...localFilters, searchTerm: newSearchTerm, page: 1 };
        setLocalFilters(updatedFilters);
        
        // Defer the parent notification to avoid setState during render
        setTimeout(() => {
          onFiltersChange(updatedFilters);
        }, 0);
      }
    }, 500); // 500ms delay

    return () => clearTimeout(timer);
  }, [debouncedSearchTerm, localFilters.searchTerm]);



  // Handle range input changes with local state (no immediate search)
  const updateLocalRangeInput = (key: string, value: string) => {
    setLocalRangeInputs((prev) => ({ ...prev, [key]: value }));
  };

  // Apply range filter when user finishes input (onBlur)
  const applyRangeFilter = (key: string, value: string) => {
    console.log("=== APPLY RANGE FILTER ===");
    console.log("Key:", key);
    console.log("Value:", value);
    console.log("Call stack:", new Error().stack);
    console.log("========================");
    
    const numericValue = value.trim() === "" ? undefined : Number(value);
    updateFilters({ [key]: numericValue } as Partial<DealUnifiedFilters>);

    // Clear local input state
    setLocalRangeInputs((prev) => {
      const newState = { ...prev };
      delete newState[key];
      return newState;
    });
  };

  // Handle text input changes with local state (no immediate search)
  const updateLocalTextInput = (key: string, value: string) => {
    setLocalTextInputs((prev) => ({ ...prev, [key]: value }));
  };

  // Apply text filter when user finishes input (onBlur)
  const applyTextFilter = (key: string, value: string) => {
    console.log("=== APPLY TEXT FILTER ===");
    console.log("Key:", key);
    console.log("Value:", value);
    console.log("Call stack:", new Error().stack);
    console.log("=======================");
    
    const textValue = value.trim() === "" ? undefined : value.trim();
    updateFilters({ [key]: textValue } as Partial<DealUnifiedFilters>);

    // Clear local input state
    setLocalTextInputs((prev) => {
      const newState = { ...prev };
      delete newState[key];
      return newState;
    });
  };

  // Apply amount filter when user finishes input (onBlur)
  const applyAmountFilter = (value: string) => {
    console.log("=== APPLY AMOUNT FILTER ===");
    console.log("Value:", value);
    console.log("========================");
    
    const numericValue = value.trim() === "" ? undefined : Math.abs(Number(value));
    updateFilters({ amount: numericValue } as Partial<DealUnifiedFilters>);

    // Clear local input state
    setLocalTextInputs((prev) => {
      const newState = { ...prev };
      delete newState.amount;
      return newState;
    });
  };

  // Handle search input changes (immediate UI update + debounced API call)
  const handleSearchInputChange = (value: string) => {
    setInputSearchTerm(value); // Immediate UI update
    setDebouncedSearchTerm(value); // Trigger debounced API call
  };



  // Get current value for range input (local state or filter value)
  const getRangeInputValue = (key: string, filterValue?: number) => {
    return localRangeInputs[key] !== undefined
      ? localRangeInputs[key]
      : filterValue || "";
  };

  // Get current value for text input (local state or filter value)
  const getTextInputValue = (key: string, filterValue?: string) => {
    return localTextInputs[key] !== undefined
      ? localTextInputs[key]
      : filterValue || "";
  };

  // Removed helper functions for investment criteria mappings - focusing on deal-specific filters only

  // Count active filters including deal-specific filters
  const getActiveFilterCount = () => {
    let count = 0;

    // Basic search filters
    if (pendingFilters.searchTerm) count++;

    // Investment criteria filters (new structure)
    if (pendingFilters.capitalPosition?.length) count++;
    if (pendingFilters.amount) count++;
    if (pendingFilters.state?.length) count++;
    if (pendingFilters.city?.length) count++;
    if (pendingFilters.propertyType?.length) count++;
    if (pendingFilters.strategy?.length) count++;

    // Deal-specific filters (keep only essential ones)
    if (pendingFilters.dealStage?.length) count++;
    if (pendingFilters.status?.length) count++;
    if (pendingFilters.published !== undefined) count++;

    return count;
  };

  const activeFilterCount = getActiveFilterCount();

  // Enhanced sort options including deal-specific fields
  const UNIFIED_SORT_OPTIONS = [
    { value: "updated_at", label: "Last Updated", icon: Clock },
    { value: "created_at", label: "Created Date", icon: Calendar },
    { value: "deal_name", label: "Deal Name", icon: Building },
    { value: "sponsor_name", label: "Sponsor Name", icon: User },
    { value: "address", label: "Address", icon: MapPin },
    { value: "ask_amount", label: "Ask Amount", icon: DollarSign },
    { value: "capital_position", label: "Capital Position", icon: Target },
    { value: "city", label: "City", icon: MapPin },
    { value: "deal_stage", label: "Deal Stage", icon: Building2 },
    { value: "deal_type", label: "Deal Type", icon: FileText },
    { value: "neighborhood", label: "Neighborhood", icon: Home },
    { value: "zip_code", label: "Zip Code", icon: MapPin },
    { value: "strategy", label: "Strategy", icon: TrendingUp },
    { value: "property_type", label: "Property Type", icon: Building2 },
    { value: "total_project_cost", label: "Total Project Cost", icon: Banknote },
  ];

  return (
    <>
      {/* Unified Filter Bar */}
      <div className="w-full bg-white border border-gray-200 rounded-xl shadow-sm mb-6">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-4">
            {/* Unified Filter Button */}
            <Button
              onClick={() => setIsFilterPanelOpen(!isFilterPanelOpen)}
              className={`flex items-center gap-3 px-6 py-3 rounded-lg shadow-md hover:shadow-lg transition-all duration-200 ${
                isFilterPanelOpen
                  ? "bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white"
                  : "bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white"
              }`}
            >
              <SlidersHorizontal className="h-5 w-5" />
              <span className="font-medium">Filters</span>
              {activeFilterCount > 0 && (
                <Badge className="bg-white/20 text-white border border-white/20 ml-1">
                  {activeFilterCount}
                </Badge>
              )}
            </Button>

            {/* Quick Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search deals, addresses, contacts, sponsors, or criteria..."
                value={inputSearchTerm}
                onChange={(e) => handleSearchInputChange(e.target.value)}
                className="pl-10 border-gray-200 focus:border-purple-500 focus:ring-1 focus:ring-purple-500"
              />
            </div>
          </div>

          {/* Sort Controls */}
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-700">
                Sort by:
              </span>
            </div>
            <div className="flex items-center gap-2">
              {/* Sort Field Selector */}
              <Select
                value={localFilters.sortBy || "updated_at"}
                onValueChange={(value) => updateFilters({ sortBy: value })}
              >
                <SelectTrigger className="w-auto min-w-[200px] border-gray-200 bg-white shadow-sm">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="max-h-[400px]">
                  {UNIFIED_SORT_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      <div className="flex items-center gap-2">
                        <option.icon className="h-4 w-4" />
                        {option.label}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              {/* Sort Direction Selector */}
              <Select
                value={localFilters.sortOrder || "desc"}
                onValueChange={(value: "asc" | "desc") => updateFilters({ sortOrder: value })}
              >
                <SelectTrigger className="w-auto min-w-[120px] border-gray-200 bg-white shadow-sm">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="desc">
                    <div className="flex items-center gap-2">
                      <ChevronDown className="h-4 w-4" />
                      Descending
                    </div>
                  </SelectItem>
                  <SelectItem value="asc">
                    <div className="flex items-center gap-2">
                      <ChevronUp className="h-4 w-4" />
                      Ascending
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      </div>

      {/* Comprehensive Right Side Filter Panel */}
      <div
        className={`fixed top-0 right-0 h-full w-[600px] bg-white border-l border-gray-200 shadow-xl transform transition-transform duration-300 ease-in-out z-50 ${
          isFilterPanelOpen ? "translate-x-0" : "translate-x-full"
        }`}
      >
        <div className="flex flex-col h-full">
          {/* Enhanced Panel Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-purple-50 via-indigo-50 to-blue-50">
            <div className="flex items-center gap-4">
              <div className="relative">
                <div className="p-3 rounded-xl bg-gradient-to-r from-purple-600 to-indigo-600 text-white shadow-lg">
                  <Building className="h-6 w-6" />
                </div>
                {activeFilterCount > 0 && (
                  <div className="absolute -top-2 -right-2 h-5 w-5 rounded-full bg-red-500 text-white text-xs flex items-center justify-center font-bold">
                    {activeFilterCount > 9 ? "9+" : activeFilterCount}
                  </div>
                )}
              </div>
              <div>
                <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
                  Filters
                  <Badge className="bg-purple-100 text-purple-700 border border-purple-200 ml-2">
                    Deal Data
                  </Badge>
                </h2>
                <p className="text-sm text-gray-600 mt-1 flex items-center gap-2">
                  <BarChart3 className="h-4 w-4" />
                  Filter deals by deal-specific data
                </p>
              </div>
            </div>

            <Button
              onClick={() => setIsFilterPanelOpen(false)}
              variant="ghost"
              size="icon"
              className="h-8 w-8 text-gray-400 hover:text-gray-600"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>

          {/* Filter Content */}
          <div className="flex-1 overflow-y-auto p-6">
            <div className="space-y-8">
              {/* Section 1: Search Filter */}
              <div className="space-y-4">
                <div className="flex items-center gap-3 pb-2 border-b border-gray-200">
                  <div className="p-2 rounded-lg bg-blue-100">
                    <Search className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">Search Filter</h3>
                    <p className="text-sm text-gray-600">Multi-field search with forgiving logic</p>
                  </div>
                </div>
                
                <div className="bg-white rounded-lg border border-gray-200 p-4">
                  <div className="space-y-3">
                    <Label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                      <Search className="h-4 w-4" />
                      Search Deals
                    </Label>
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <Input
                        placeholder="Search deals, addresses, contacts, sponsors..."
                        value={inputSearchTerm}
                        onChange={(e) => handleSearchInputChange(e.target.value)}
                        className="pl-10 border-gray-200 focus:border-purple-500 focus:ring-1 focus:ring-purple-500"
                        disabled={isLoading}
                      />
                    </div>
                    <div className="text-xs text-gray-500 space-y-1">
                      <p><strong>Searches across:</strong> Deal Name, Contact Name, Contact Email, and Address</p>
                      <p><strong>Features:</strong> Case-insensitive, partial matching, punctuation handling</p>
                      <p><strong>Synced with:</strong> Main search field above</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Section 2: Investment Criteria */}
              <div className="space-y-4">
                <div className="flex items-center gap-3 pb-2 border-b border-gray-200">
                  <div className="p-2 rounded-lg bg-green-100">
                    <Target className="h-5 w-5 text-green-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">Investment Criteria</h3>
                    <p className="text-sm text-gray-600">Dynamic filters based on capital position type</p>
                  </div>
                </div>
                
                <div className="space-y-4">
                  {/* Capital Position Filter */}
                  <div className="bg-white rounded-lg border border-gray-200 p-4">
                    <Label className="text-sm font-medium text-gray-700 flex items-center gap-2 mb-3">
                      <Target className="h-4 w-4" />
                      Capital Position
                    </Label>
                    <ReactMultiSelect
                      options={capitalPositions}
                      selected={pendingFilters.capitalPosition || []}
                      onChange={(selected) => updatePendingFilters({ capitalPosition: selected })}
                      placeholder="Select capital position types..."
                      className="w-full"
                    />
                  </div>

                  {/* Deal Size Filter */}
                  <div className="bg-white rounded-lg border border-gray-200 p-4">
                    <Label className="text-sm font-medium text-gray-700 flex items-center gap-2 mb-3">
                      <DollarSign className="h-4 w-4" />
                      Deal Size (Ask Amount)
                    </Label>
                    <Input
                      type="number"
                      placeholder="Enter amount to match against NSF amount"
                      value={localTextInputs.amount || (pendingFilters.amount ? Math.abs(pendingFilters.amount).toString() : "")}
                      onChange={(e) => updateLocalTextInput("amount", e.target.value)}
                      onBlur={() => applyAmountFilter(localTextInputs.amount || "")}
                      className="border-gray-200 focus:border-purple-500 focus:ring-1 focus:ring-purple-500"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Matches against any NSF amount field where is_required = true
                    </p>
                  </div>

                  {/* Location Filters */}
                  <div className="bg-white rounded-lg border border-gray-200 p-4">
                    <Label className="text-sm font-medium text-gray-700 flex items-center gap-2 mb-3">
                      <MapPin className="h-4 w-4" />
                      Location
                    </Label>
                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <Label className="text-xs text-gray-500">State</Label>
                        <ReactMultiSelect
                          options={states}
                          selected={pendingFilters.state || []}
                          onChange={(selected) => updatePendingFilters({ state: selected })}
                          placeholder="Select states..."
                          className="w-full"
                        />
                      </div>
                      <div>
                        <Label className="text-xs text-gray-500">City</Label>
                        <ReactMultiSelect
                          options={cities}
                          selected={pendingFilters.city || []}
                          onChange={(selected) => updatePendingFilters({ city: selected })}
                          placeholder="Select cities..."
                          className="w-full"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Property Type Filter */}
                  <div className="bg-white rounded-lg border border-gray-200 p-4">
                    <Label className="text-sm font-medium text-gray-700 flex items-center gap-2 mb-3">
                      <Building2 className="h-4 w-4" />
                      Property Type
                    </Label>
                    <ReactMultiSelect
                      options={propertyTypes}
                      selected={pendingFilters.propertyType || []}
                      onChange={(selected) => updatePendingFilters({ propertyType: selected })}
                      placeholder="Select property types..."
                      className="w-full"
                    />
                  </div>

                  {/* Strategy Filter */}
                  <div className="bg-white rounded-lg border border-gray-200 p-4">
                    <Label className="text-sm font-medium text-gray-700 flex items-center gap-2 mb-3">
                      <TrendingUp className="h-4 w-4" />
                      Strategy
                    </Label>
                    <ReactMultiSelect
                      options={strategies}
                      selected={pendingFilters.strategy || []}
                      onChange={(selected) => updatePendingFilters({ strategy: selected })}
                      placeholder="Select strategies..."
                      className="w-full"
                    />
                  </div>
                </div>
              </div>

              {/* Removed Investment Focus Group - focusing on deal-specific filters only */}

        

              {/* Removed Deal Economics Group - focusing on basic deal filters only */}

              {/* Geographic Focus Group - Simplified */}
 
              {/* Property & Investment Focus Group */}
       
            </div>
          </div>

          {/* Enhanced Footer */}
          <div className="border-t border-gray-200 p-6 bg-gradient-to-r from-gray-50 to-purple-50">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-600">
                {activeFilterCount > 0 ? (
                  <div className="flex items-center gap-2">
                    <Sparkles className="h-4 w-4 text-purple-600" />
                    <span className="font-medium text-gray-700">
                      {activeFilterCount} Filter
                      {activeFilterCount !== 1 ? "s" : ""} Active
                    </span>
                    <Badge className="bg-purple-100 text-purple-700 border border-purple-200">
                      Deal Data
                    </Badge>
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    <Filter className="h-4 w-4 text-gray-400" />
                    <span>
                      No filters applied - showing all deal data
                    </span>
                  </div>
                )}
              </div>

              <div className="flex items-center gap-3">
                <Button
                  onClick={onClearFilters}
                  variant="outline"
                  size="sm"
                  className="text-gray-600 hover:text-gray-800"
                >
                  Clear All
                </Button>
                <Button
                  onClick={applyFilters}
                  className="bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white"
                >
                  Apply Filters
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
