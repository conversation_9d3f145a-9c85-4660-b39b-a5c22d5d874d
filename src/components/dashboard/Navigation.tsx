import React, { useState } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { signOut } from "next-auth/react";
import type { Session } from "next-auth";
import { NotificationBell } from "@/components/notifications/NotificationBell";
import {
  CircleDollarSign,
  Code,
  Newspaper,
  Users,
  User,
  Mail,
  Building2,
  Handshake,
  FileText,
  Calculator,
  Settings,
  LogOut,
  Database,
  Briefcase,
  FileCheck,
  HandshakeIcon,
  FileIcon,
  LineChart,
  Shield,
  Zap,
  Network,
  Send,
  Activity,
  BarChart3,
  Map,
  TrendingUp,
  AlertTriangle,
  Home,
} from "lucide-react";

export const Navigation = ({
  activeSection,
  setActiveSection,
  isCollapsed,
  setIsCollapsed,
  accessLevel,
  session,
}: {
  activeSection: string;
  setActiveSection: (section: string) => void;
  isCollapsed: boolean;
  setIsCollapsed: (collapsed: boolean) => void;
  accessLevel: string;
  session: Session;
}) => {
  const [showProfile, setShowProfile] = useState(false);
  const pathname = usePathname();

  // Define which sections are available for different user roles
  const GUEST_SECTIONS = ["projections"]; // Only allow projections for guests
  const ADMIN_ONLY_SECTIONS = ["configuration", "processing", "admin-panel"]; // Admin-only sections

  const canAccessSection = (sectionName: string) => {
    if (accessLevel === "admin") return true;
    if (accessLevel === "guest") {
      return GUEST_SECTIONS.includes(sectionName);
    }
    // Regular users can access everything except admin-only sections
    return !ADMIN_ONLY_SECTIONS.includes(sectionName);
  };

  const handleWhitepaperClick = () => {
    const domain = window.location.hostname;
    const wpDomain = domain.includes("anax.my")
      ? "wp.anax.my"
      : "wp.anax.cloud";
    window.open(`https://${wpDomain}`, "_blank");
  };

  const handleLogout = async () => {
    await signOut({ 
      callbackUrl: "/login",
      redirect: true 
    });
  };

  const navItems = [
    {
      id: "entity",
      name: "Entity",
      icon: Users,
      color: "text-purple-500",
      path: "/dashboard/entity",
    },
    {
      id: "upload",
      name: "Upload",
      icon: Database,
      color: "text-blue-500",
      path: "/dashboard/upload",
    },
    {
      id: "mapping",
      name: "Mapping",
      icon: Map,
      color: "text-orange-500",
      path: "/dashboard/mapping",
    },
    {
      id: "smartlead",
      name: "Smartlead",
      icon: Send,
      color: "text-pink-500",
      path: "/dashboard/smartlead",
    },
    {
      id: "processing",
      name: "Processing",
      icon: Activity,
      color: "text-cyan-500",
      path: "/dashboard/processing",
    },
    {
      id: "data-quality",
      name: "Data Quality",
      icon: FileCheck,
      color: "text-teal-500",
      path: "/dashboard/data-quality",
    },
    {
      id: "duplicates",
      name: "Duplicates",
      icon: AlertTriangle,
      color: "text-red-500",
      path: "/dashboard/duplicates",
    },
    {
      id: "articles",
      name: "Articles",
      icon: FileText,
      color: "text-gray-500",
      path: "/dashboard/articles",
    },
    {
      id: "deals",
      name: "Deals",
      icon: Handshake,
      color: "text-blue-500",
      path: "/dashboard/deals",
    },
    {
      id: "projections",
      name: "Projections",
      icon: LineChart,
      color: "text-violet-500",
      path: "/dashboard/projections",
    },
    {
      id: "homepage",
      name: "Homepage",
      icon: Home,
      color: "text-green-600",
      path: "/dashboard/homepage",
    },
    {
      id: "configuration",
      name: "Configuration",
      icon: Settings,
      color: "text-gray-600",
      path: "/dashboard/configuration",
    },
    {
      id: "admin-panel",
      name: "Admin Panel",
      icon: Shield,
      color: "text-red-600",
      path: "/admin",
    },
  ];

  // Determine active section from path
  const getActiveSectionFromPath = () => {
    if (!pathname) return activeSection;

    // Check if we're in subsections
    if (pathname.startsWith("/dashboard/people/")) {
      return "people";
    }
    if (pathname.startsWith("/dashboard/articles/")) {
      return "articles";
    }

    // Check other sections
    for (const item of navItems) {
      if (pathname === item.path || pathname.startsWith(`${item.path}/`)) {
        return item.id;
      }
    }

    return activeSection;
  };

  // Function to handle navigation click
  const handleNavClick = (item: (typeof navItems)[0]) => {
    setActiveSection(item.id);
  };

  const currentActiveSection = getActiveSectionFromPath();

  return (
    <div className="bg-white p-3 fixed left-0 top-0 h-full w-40 border-r flex flex-col">
      <div className="flex items-center justify-between mb-8">
        <Link
          href="/dashboard"
          className="relative h-8 flex-1 hover:opacity-80 transition-opacity"
          onClick={() => setActiveSection("dashboard")}
        >
          <CircleDollarSign
            className={`h-8 w-8 text-blue-600 ${
              isCollapsed ? "mx-auto" : "ml-2"
            }`}
          />
          <span
            className={`absolute left-12 top-1/2 -translate-y-1/2 text-lg font-bold transition-opacity duration-300 ${
              isCollapsed ? "opacity-0" : "opacity-100"
            }`}
          >
            ANAX
          </span>
        </Link>
        {!isCollapsed && (
          <div className="mr-2">
            <NotificationBell />
          </div>
        )}
      </div>

      <nav className="space-y-2 flex-1">
        {navItems.map(
          (item) =>
            canAccessSection(item.id) && 
            // Only show admin-only sections for admin users
            (item.id !== "configuration" || accessLevel === "admin") &&
            (item.id !== "admin-panel" || accessLevel === "admin") && (
              <Link
                key={item.id}
                href={item.path}
                onClick={() => handleNavClick(item)}
                className={`w-full flex items-center space-x-2.5 px-3 py-2.5 rounded-lg transition-all ${
                  currentActiveSection === item.id
                    ? "bg-blue-50 text-blue-600"
                    : "hover:bg-gray-50"
                }`}
              >
                <item.icon className={`h-6 w-6 ${item.color}`} />
                <span className="text-base font-medium">{item.name}</span>
              </Link>
            )
        )}
      </nav>

      <button
        onClick={handleWhitepaperClick}
        className="w-full flex items-center space-x-2.5 px-3 py-2.5 rounded-lg transition-all hover:bg-gray-50 mb-2"
      >
        <Zap className="h-6 w-6 text-yellow-500" />
        <span className="text-base font-medium">Whitepaper</span>
      </button>

      <div className="border-t pt-3 space-y-2">
        {/* User Profile Section */}
        <div className="flex items-center space-x-3 px-3 py-2 rounded-lg hover:bg-gray-50">
          <div className="flex-shrink-0">
            {session.user?.image ? (
              <img
                src={session.user.image}
                alt={session.user.name || 'User'}
                className="h-8 w-8 rounded-full"
              />
            ) : (
              <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                <User className="h-4 w-4 text-blue-600" />
              </div>
            )}
          </div>
          {!isCollapsed && (
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900 truncate">
                {session.user?.name || session.user?.email}
              </p>
              <p className="text-xs text-gray-500 capitalize">
                {accessLevel}
              </p>
            </div>
          )}
        </div>

        {/* Settings Link */}
        <Link
          href="/dashboard/settings"
          className="w-full flex items-center justify-center space-x-2 p-2 hover:bg-gray-50 rounded-lg text-sm text-gray-600"
        >
          <Settings className="h-4 w-4" />
          <span>Settings</span>
        </Link>

        {/* Logout Button */}
        <button
          onClick={handleLogout}
          className="w-full flex items-center justify-center space-x-2 p-2 hover:bg-gray-50 rounded-lg text-sm text-red-600"
        >
          <LogOut className="h-4 w-4" />
          <span>Sign Out</span>
        </button>
      </div>
    </div>
  );
};
