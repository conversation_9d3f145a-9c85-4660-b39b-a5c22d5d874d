'use client'

import { useState, useMemo, useEffect } from 'react'
import { CompanyDetail } from '../shared/types'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { UserPlus, Search, X, Loader2, CheckSquare, Square, Play, RefreshCw, Sparkles, Mail, PenTool, AlertTriangle, CheckCircle, XCircle, Send, Settings2, Activity } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { useToast } from '@/hooks/use-toast'
import AddContact from '@/components/dashboard/people/AddContact'
import ContactCard from '@/components/dashboard/people/list-components/ContactCard'
import { UnifiedContactData } from '@/components/dashboard/people/shared/types'

interface ContactsResponse {
  success: boolean;
  contacts: UnifiedContactData[];
  stats: {
    total_contacts: number;
    contacts_with_email: number;
    contacts_with_linkedin: number;
    verified_emails: number;
    enriched_contacts: number;
  };
}

// Processing stages for contacts
type ContactProcessingStage = 'email_validation' | 'contact_enrichment' | 'contact_enrichment_v2' | 'contact_investment_criteria' | 'email_generation' | 'smartlead_sync';

interface ProcessingJob {
  stage: ContactProcessingStage;
  isExecuting: boolean;
}

interface CompanyContactsProps {
  company: CompanyDetail
}

export default function CompanyContacts({ company }: CompanyContactsProps) {
  const router = useRouter()
  const { toast } = useToast()
  const [showAddContact, setShowAddContact] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  
  // Lazy loading state for contacts
  const [contacts, setContacts] = useState<UnifiedContactData[]>([])
  const [contactsStats, setContactsStats] = useState<ContactsResponse['stats'] | null>(null)
  const [loadingContacts, setLoadingContacts] = useState(false)
  const [contactsLoaded, setContactsLoaded] = useState(false)
  const [selectedContacts, setSelectedContacts] = useState<Set<number>>(new Set())
  const [selectAll, setSelectAll] = useState(false)
  
  // Processing state
  const [processingJobs, setProcessingJobs] = useState<ProcessingJob[]>([
    { stage: 'email_validation', isExecuting: false },
    { stage: 'contact_enrichment_v2', isExecuting: false },
    { stage: 'contact_investment_criteria', isExecuting: false },
    { stage: 'email_generation', isExecuting: false },
    { stage: 'smartlead_sync', isExecuting: false }
  ])
  
  // Smartlead campaign state
  const [campaigns, setCampaigns] = useState<Array<{id: string, name: string}>>([])
  const [selectedCampaignId, setSelectedCampaignId] = useState<string>('')
  const [loadingCampaigns, setLoadingCampaigns] = useState(false)
  
  // Load contacts when component mounts
  useEffect(() => {
    if (company.company_id && !contactsLoaded && !loadingContacts) {
      loadContacts()
    }
  }, [company.company_id])

  // Load campaigns from Smartlead
  useEffect(() => {
    const loadCampaigns = async () => {
      setLoadingCampaigns(true)
      try {
        const response = await fetch('/api/smartlead/campaigns')
        if (!response.ok) throw new Error('Failed to fetch campaigns')
        
        const data = await response.json()
        const campaignsList = Array.isArray(data.campaigns) ? data.campaigns : (Array.isArray(data) ? data : [])
        setCampaigns(campaignsList.map((campaign: any, index: number) => {
          // Safely convert id to string
          let campaignId = ''
          if (campaign.id !== null && campaign.id !== undefined && typeof campaign.id !== 'object') {
            campaignId = String(campaign.id)
          } else {
            campaignId = `campaign-${index}`
          }
          
          return {
            id: campaignId,
            name: (typeof campaign.name === 'string' || typeof campaign.name === 'number') ? String(campaign.name) : `Campaign ${index + 1}`
          }
        }))
      } catch (error) {
        console.error('Error loading campaigns:', error)
        toast({
          title: "Campaign Loading Failed",
          description: "Failed to load Smartlead campaigns",
          variant: "destructive",
        })
      } finally {
        setLoadingCampaigns(false)
      }
    }

    loadCampaigns()
  }, [toast])

  const loadContacts = async () => {
    if (!company.company_id || loadingContacts || contactsLoaded) return
    
    setLoadingContacts(true)
    try {
      const response = await fetch(`/api/companies/${company.company_id}/contacts`)
      const data: ContactsResponse = await response.json()
      
      if (data.success) {
        setContacts(data.contacts)
        setContactsStats(data.stats)
        setContactsLoaded(true)
      }
    } catch (error) {
      console.error('Error loading contacts:', error)
    } finally {
      setLoadingContacts(false)
    }
  }
  
  // Add the handleOpenContact function
  const handleOpenContact = (contactId: number) => {
    // Navigate to contact detail page
    router.push(`/dashboard/people/${contactId}`);
  };

  // Handle contact selection
  const handleToggleSelection = (contactId: number, event: React.MouseEvent | React.ChangeEvent) => {
    event.stopPropagation();
    setSelectedContacts(prev => {
      const newSet = new Set(prev);
      if (newSet.has(contactId)) {
        newSet.delete(contactId);
      } else {
        newSet.add(contactId);
      }
      return newSet;
    });
  };

  // Handle select all toggle
  const handleSelectAll = () => {
    if (selectAll) {
      setSelectedContacts(new Set());
      setSelectAll(false);
    } else {
      const allContactIds = contacts.map(contact => contact.contact_id).filter((id): id is number => id !== undefined);
      setSelectedContacts(new Set(allContactIds));
      setSelectAll(true);
    }
  };

  // Update selectAll state based on current selection
  useEffect(() => {
    if (contacts.length === 0) {
      setSelectAll(false);
      return;
    }
    
    const allContactIds = contacts.map(contact => contact.contact_id).filter((id): id is number => id !== undefined);
    const allSelected = allContactIds.every(id => selectedContacts.has(id));
    setSelectAll(allSelected);
  }, [selectedContacts, contacts]);

  // Handle adding new contact
  const handleAddContact = () => {
    setShowAddContact(true);
  };

  const handleBackFromAddContact = () => {
    setShowAddContact(false);
    // Refresh contacts data to show new contact
    setContactsLoaded(false)
    loadContacts()
  };

  // Get stage configuration
  const getStageConfig = (stage: ContactProcessingStage) => {
    const configs = {
      email_validation: {
        title: 'Email Validation',
        icon: Mail,
        color: 'bg-blue-500',
        description: 'Verify email addresses'
      },
      contact_enrichment: {
        title: 'Contact Enrichment',
        icon: Sparkles,
        color: 'bg-purple-500',
        description: 'Enrich contact data'
      },
      contact_enrichment_v2: {
        title: 'Contact Enrichment V2',
        icon: Sparkles,
        color: 'bg-purple-600',
        description: 'Enhanced contact enrichment with additional fields'
      },
      contact_investment_criteria: {
        title: 'Investment Criteria',
        icon: AlertTriangle,
        color: 'bg-rose-500',
        description: 'Extract personalized investment criteria'
      },
      email_generation: {
        title: 'Email Generation',
        icon: PenTool,
        color: 'bg-pink-500',
        description: 'Generate personalized emails'
      },
      smartlead_sync: {
        title: 'Smartlead Sync',
        icon: Send,
        color: 'bg-green-500',
        description: 'Sync Smartlead IDs'
      }
    };
    return configs[stage];
  };

  // Get processing status for a contact
  const getContactProcessingStatus = (contact: UnifiedContactData, stage: ContactProcessingStage) => {
    switch (stage) {
      case 'email_validation':
        return contact.email_verification_status;
      case 'contact_enrichment_v2':
        return contact.contact_enrichment_v2_status || 'pending';
      case 'contact_investment_criteria':
        return contact.contact_investment_criteria_status || 'pending';
      case 'email_generation':
        return contact.email_generation_status;
      case 'smartlead_sync':
        return contact.smartlead_status || 'pending';
      default:
        return null;
    }
  };

  // Get status summary for selected contacts
  const getSelectedContactsStatusSummary = (stage: ContactProcessingStage) => {
    if (selectedContacts.size === 0) return null;

    const statusCounts: { [key: string]: number } = {};
    let total = 0;

    contacts.forEach(contact => {
      if (contact.contact_id && selectedContacts.has(contact.contact_id)) {
        const status = getContactProcessingStatus(contact, stage);
        if (status) {
          statusCounts[status] = (statusCounts[status] || 0) + 1;
        }
        total++;
      }
    });

    return { statusCounts, total };
  };

  // Handle processing operations
  const executeProcessingJob = async (stage: ContactProcessingStage) => {
    if (selectedContacts.size === 0) return;

    const contactIds = Array.from(selectedContacts);
    
    // Update processing state
    setProcessingJobs(prev => 
      prev.map(job => 
        job.stage === stage 
          ? { ...job, isExecuting: true }
          : job
      )
    );

    try {
      // Check for campaign requirement for stages that need it
      if ((stage === 'smartlead_sync' || stage === 'email_generation') && !selectedCampaignId) {
        toast({
          title: "Campaign Required",
          description: `Please select a campaign for ${getStageConfig(stage).title}`,
          variant: "destructive",
        });
        return;
      }

      // Handle all processing stages through the processing trigger API
      const jobOptions: any = {
        multiIds: contactIds,
        limit: contactIds.length,
        batchSize: Math.min(100, contactIds.length),
        filters: {
          // Add company filter to only process contacts from this company
          companyIds: [company.company_id]
        }
      }

      // Add campaign ID for stages that need it
      if ((stage === 'email_generation' || stage === 'smartlead_sync') && selectedCampaignId) {
        jobOptions.campaignId = selectedCampaignId
      }

      const response = await fetch(`${process.env.API_BASE_URL || ''}/api/processing/trigger`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'execute_manual',
          stage,
          entityType: 'contact',
          options: jobOptions
        })
      });

      const data = await response.json();
      if (data.success) {
        toast({
          title: "Processing Started",
          description: `Successfully triggered ${getStageConfig(stage).title} for ${contactIds.length} contact${contactIds.length !== 1 ? 's' : ''}`,
        });
        // Refresh contacts after a short delay to show updated status
        setTimeout(() => {
          setContactsLoaded(false)
          loadContacts()
        }, 2000);
      } else {
        console.error(`Failed to trigger ${stage}:`, data.error);
        toast({
          title: "Processing Failed",
          description: `Failed to trigger ${getStageConfig(stage).title}: ${data.error}`,
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error(`Error executing ${stage}:`, error);
      toast({
        title: "Processing Error",
        description: `Error executing ${getStageConfig(stage).title}: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: "destructive",
      });
    } finally {
      // Reset processing state
      setProcessingJobs(prev => 
        prev.map(job => 
          job.stage === stage 
            ? { ...job, isExecuting: false }
            : job
      )
      );
    }
  };

  // Filter contacts based on search term
  const filteredContacts = useMemo(() => {
    if (!searchTerm.trim()) {
      return contacts;
    }

    const searchLower = searchTerm.toLowerCase();
    
    return contacts.filter(contact => 
      contact.first_name?.toLowerCase().includes(searchLower) ||
      contact.last_name?.toLowerCase().includes(searchLower) ||
      contact.full_name?.toLowerCase().includes(searchLower) ||
      contact.title?.toLowerCase().includes(searchLower) ||
      contact.headline?.toLowerCase().includes(searchLower) ||
      contact.email?.toLowerCase().includes(searchLower) ||
      contact.personal_email?.toLowerCase().includes(searchLower)
    );
  }, [contacts, searchTerm]);

  const totalFilteredContacts = filteredContacts.length;
  const totalContacts = contacts.length;

  // Prepare pre-selected company data for the AddContact component (simplified for V2)
  const preSelectedCompany = {
    company_id: company.company_id,
    company_name: company.company_name || '',
    company_website: company.company_website || '',
    industry: company.industry || '',
    company_address: company.company_address || '',
    company_city: company.company_city || '',
    company_state: company.company_state || '',
    company_country: company.company_country || '',
    // V2 company data mapping - convert numbers to strings for compatibility
    extracted_data: {
      companytype: company.company_type,
      businessmodel: company.business_model,
      fundsize: company.fund_size ? String(company.fund_size) : undefined,
      aum: company.aum ? String(company.aum) : undefined,
      headquarters: company.company_address,
      foundedyear: company.founded_year,
      numberofemployees: company.number_of_employees ? String(company.number_of_employees) : undefined,
      investmentfocus: company.investment_focus,
      partnerships: company.partnerships,
      key_executives: company.key_executives,
      annual_revenue: company.annual_revenue ? String(company.annual_revenue) : undefined,
      market_capitalization: company.market_capitalization ? String(company.market_capitalization) : undefined,
      office_locations: company.office_locations,
      transactions_completed_last_12m: company.transactions_completed_last_12m ? String(company.transactions_completed_last_12m) : undefined
    }
  };

  // If showing AddContact, render it instead
  if (showAddContact) {
    return (
      <AddContact
        onBack={handleBackFromAddContact}
        companyId={company.company_id?.toString()}
        preSelectedCompany={preSelectedCompany}
      />
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with action buttons */}
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">
          Contacts ({totalFilteredContacts}{searchTerm && totalFilteredContacts !== totalContacts ? ` of ${totalContacts}` : ''})
          {contactsStats && (
            <span className="text-sm font-normal text-gray-500 ml-2">
              ({contactsStats.verified_emails} verified)
            </span>
          )}
        </h2>
        <Button className="flex items-center gap-2" onClick={handleAddContact}>
          <UserPlus className="h-4 w-4" />
          Add Contact
        </Button>
      </div>

      {/* Contact Stats */}
      {contactsStats && (
        <div className="grid grid-cols-4 gap-4 mb-4">
          <div className="bg-blue-50 p-3 rounded-lg text-center">
            <div className="text-2xl font-bold text-blue-600">{contactsStats.total_contacts}</div>
            <div className="text-sm text-blue-700">Total</div>
          </div>
          <div className="bg-green-50 p-3 rounded-lg text-center">
            <div className="text-2xl font-bold text-green-600">{contactsStats.contacts_with_email}</div>
            <div className="text-sm text-green-700">With Email</div>
          </div>
          <div className="bg-purple-50 p-3 rounded-lg text-center">
            <div className="text-2xl font-bold text-purple-600">{contactsStats.contacts_with_linkedin}</div>
            <div className="text-sm text-purple-700">LinkedIn</div>
          </div>
          <div className="bg-orange-50 p-3 rounded-lg text-center">
            <div className="text-2xl font-bold text-orange-600">{contactsStats.enriched_contacts}</div>
            <div className="text-sm text-orange-700">Enriched</div>
          </div>
        </div>
      )}

      {/* Search Bar */}
      <div className="relative">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            type="text"
            placeholder="Search contacts by name, title, email..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 pr-10"
          />
          {searchTerm && (
            <button
              onClick={() => setSearchTerm('')}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 hover:text-gray-600"
            >
              <X className="h-4 w-4" />
            </button>
          )}
        </div>
      </div>

      {/* Selection and Processing Controls */}
      {contacts.length > 0 && (
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={selectAll}
                    onChange={() => handleSelectAll()}
                    className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-sm font-medium">
                    {selectedContacts.size > 0 
                      ? `${selectedContacts.size} contact${selectedContacts.size !== 1 ? 's' : ''} selected`
                      : 'Select contacts for processing'
                    }
                  </span>
                </div>
                {selectedContacts.size > 0 && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setSelectedContacts(new Set())}
                  >
                    Clear Selection
                  </Button>
                )}
              </div>
            </CardTitle>
          </CardHeader>
          
          {selectedContacts.size > 0 && (
            <CardContent>
              <div className="space-y-4">
                <div className="text-sm text-gray-600">
                  Processing operations for {selectedContacts.size} selected contact{selectedContacts.size !== 1 ? 's' : ''}
                </div>
                
                {/* Campaign Selection for Smartlead Sync and Email Generation */}
                <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <Send className="h-4 w-4 text-green-600" />
                    <span className="text-sm font-medium text-green-800">Campaign Selection</span>
                  </div>
                  <Select
                    value={selectedCampaignId}
                    onValueChange={setSelectedCampaignId}
                    disabled={loadingCampaigns}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder={loadingCampaigns ? "Loading campaigns..." : "Select campaign for Smartlead sync and Email Generation"} />
                    </SelectTrigger>
                    <SelectContent>
                      {campaigns.map(campaign => (
                        <SelectItem key={campaign.id} value={campaign.id}>
                          {campaign.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {selectedCampaignId && (
                    <p className="text-xs text-green-600 mt-1">
                      Campaign selected: {campaigns.find(c => c.id === selectedCampaignId)?.name}
                    </p>
                  )}
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-5 gap-4">
                  {processingJobs.map((job) => {
                    const config = getStageConfig(job.stage);
                    const IconComponent = config.icon;
                    const statusSummary = getSelectedContactsStatusSummary(job.stage);
                    
                    return (
                      <Card key={job.stage} className="border-l-4" style={{ borderLeftColor: config.color.replace('bg-', '') }}>
                        <CardContent className="p-4">
                          <div className="flex items-center gap-2 mb-3">
                            <IconComponent className="h-4 w-4" />
                            <h3 className="font-medium">{config.title}</h3>
                          </div>
                          
                          {statusSummary && job.stage !== 'smartlead_sync' && (
                            <div className="mb-3 text-xs text-gray-600">
                              <div className="flex flex-wrap gap-1 mb-2">
                                {Object.entries(statusSummary.statusCounts).map(([status, count]) => (
                                  <Badge key={status} variant="outline" className="text-xs">
                                    {status}: {count}
                                  </Badge>
                                ))}
                              </div>
                            </div>
                          )}
                          
                          {job.stage === 'smartlead_sync' && (
                            <div className="mb-3 text-xs text-gray-600">
                              {!selectedCampaignId && (
                                <p className="text-amber-600 mt-1">⚠️ Select campaign above</p>
                              )}
                            </div>
                          )}
                          
                          {job.stage === 'email_generation' && (
                            <div className="mb-3 text-xs text-gray-600">
                              {!selectedCampaignId && (
                                <p className="text-amber-600 mt-1">⚠️ Select campaign above</p>
                              )}
                            </div>
                          )}
                          
                          <Button
                            onClick={() => executeProcessingJob(job.stage)}
                            disabled={job.isExecuting || ((job.stage === 'smartlead_sync' || job.stage === 'email_generation') && !selectedCampaignId)}
                            className="w-full"
                            size="sm"
                          >
                            {job.isExecuting ? (
                              <>
                                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                                Processing...
                              </>
                            ) : (
                              <>
                                <Play className="h-4 w-4 mr-2" />
                                Process {selectedContacts.size} Contact{selectedContacts.size !== 1 ? 's' : ''}
                              </>
                            )}
                          </Button>
                        </CardContent>
                      </Card>
                    );
                  })}
                </div>
              </div>
            </CardContent>
          )}
        </Card>
      )}
      
      {loadingContacts ? (
        <div className="flex items-center justify-center py-8">
          <Loader2 className="h-6 w-6 animate-spin mr-2" />
          <span>Loading contacts...</span>
        </div>
      ) : filteredContacts.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
          {filteredContacts.map((contact) => (
            <ContactCard 
              key={contact.contact_id}
              contact={contact}
              onSelectContact={handleOpenContact}
              isSelected={selectedContacts.has(contact.contact_id!)}
              onToggleSelection={handleToggleSelection}
              showDeleteButton={false}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-16 bg-white rounded-lg border border-gray-100">
          <div className="h-16 w-16 mx-auto text-gray-300 mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {searchTerm ? 'No contacts found' : 'No contacts found'}
          </h3>
          <p className="text-gray-500 mb-6">
            {searchTerm 
              ? `No contacts match "${searchTerm}". Try a different search term.`
              : "This company doesn't have any contacts yet."
            }
          </p>
        </div>
      )}
    </div>
  )
}
