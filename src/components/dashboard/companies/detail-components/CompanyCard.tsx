'use client'

import { useState } from 'react'
import { Company } from '../shared/types'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog'
import { useToast } from '@/hooks/use-toast'
import { MapPin, Users, ExternalLink, Building, ArrowRight, Globe, Briefcase, Calendar, DollarSign, Clock, BarChart3, Home, LayoutGrid, Trash2, Loader2 } from 'lucide-react'
import { getCompanyInitials, getIndustryColor, formatLocation } from '../shared/utils'
import { useRouter } from 'next/navigation'
import { cn } from '@/lib/utils'

interface CompanyCardProps {
  company: Company
  isSelected?: boolean
  onToggleSelection?: (companyId: number, event: React.MouseEvent | React.ChangeEvent) => void
}

export default function CompanyCard({ company, isSelected = false, onToggleSelection }: CompanyCardProps) {
  const router = useRouter()
  const { toast } = useToast()
  const [isDeleting, setIsDeleting] = useState(false)
  
  const industryColorClass = getIndustryColor(company.industry || '')
  const companyInitials = getCompanyInitials(company.company_name)
  const location = formatLocation(company)
  
  // Use either founded_year or foundedyear
  const foundingYear = company.founded_year || company.foundedyear
  
  // Check if company has overview data
  const hasOverviewData = company.companytype || 
    (company.propertytypes && company.propertytypes.length > 0) ||
    (company.investmentfocus && company.investmentfocus.length > 0) ||
    (company.geographicfocus && company.geographicfocus.length > 0) ||
    company.aum ||
    company.dealsize ||
    company.holdperiod ||
    company.targetreturn

  // Handle delete company
  const handleDeleteCompany = async () => {
    if (!company.company_id) {
      toast({
        title: "Error",
        description: "Company ID is required to delete company",
        variant: "destructive",
      });
      return;
    }

    setIsDeleting(true);

    try {
      // First, get all investment criteria for this company
      const icResponse = await fetch(`/api/investment-criteria-entity/company/${company.company_id}`);
      const icData = await icResponse.json();
      
      // Delete all investment criteria for this company
      if (icResponse.ok && icData.success && icData.data.criteriaList) {
        for (const criteria of icData.data.criteriaList) {
          try {
            await fetch(`/api/investment-criteria-entity/company/${company.company_id}`, {
              method: 'DELETE',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                criteria_id: criteria.investment_criteria_id
              })
            });
          } catch (icError) {
            console.warn(`Failed to delete investment criteria ${criteria.investment_criteria_id}:`, icError);
            // Continue with company deletion even if IC deletion fails
          }
        }
      }

      // Delete web crawler data (company_web_pages)
      try {
        await fetch(`/api/companies/${company.company_id}/web-pages`, {
          method: 'DELETE',
        });
      } catch (webPagesError) {
        console.warn(`Failed to delete web pages for company ${company.company_id}:`, webPagesError);
        // Continue with company deletion even if web pages deletion fails
      }

      // Now delete the company
      const response = await fetch(`/api/companies?companyId=${company.company_id}`, {
        method: 'DELETE',
      });

      const data = await response.json();

      if (response.ok && data.success) {
        toast({
          title: "Company Deleted",
          description: `"${company.company_name}" has been successfully deleted`,
        });
        
        // Refresh the page to update the company list
        window.location.reload();
      } else {
        throw new Error(data.error || 'Failed to delete company');
      }
    } catch (error) {
      console.error('Error deleting company:', error);
      toast({
        title: "Delete Error",
        description: error instanceof Error ? error.message : 'Failed to delete company',
        variant: "destructive",
      });
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <div 
      className="bg-white border border-gray-100 rounded-lg overflow-hidden hover:shadow-md transition-all duration-200 h-full flex flex-col cursor-pointer"
      onClick={() => router.push(`/dashboard/companies/${company.company_id}`)}
    >
      {/* Colored accent top border based on industry */}
      <div className={cn("h-1.5 w-full", industryColorClass)} />
      
      <div className="p-5 flex-grow flex flex-col">
        {/* Header with delete button */}
        <div className="flex justify-between items-start mb-4">
          <div className="flex items-start gap-4 flex-1">
            {onToggleSelection && (
              <input
                type="checkbox"
                checked={isSelected}
                onChange={(e) => onToggleSelection(company.company_id, e)}
                onClick={(e) => e.stopPropagation()}
                className="mt-1 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
            )}
            <Avatar className="h-12 w-12">
              <AvatarFallback className={cn("text-white font-semibold", industryColorClass)}>
                {companyInitials}
              </AvatarFallback>
            </Avatar>
            
            <div className="flex-1 min-w-0">
              <h3 className="font-semibold text-lg leading-tight mb-1 text-gray-900 hover:text-blue-600 transition-colors">
                {company.company_name}
              </h3>
              
              {company.industry && (
                <div className="mb-2 flex items-center">
                  <BarChart3 className="h-3.5 w-3.5 mr-1.5 flex-shrink-0 text-gray-400" />
                  <span className="text-sm text-gray-600">{company.industry}</span>
                </div>
              )}
              
              <div className="flex flex-wrap gap-x-4 gap-y-1.5 text-sm text-gray-500">
                {location && (
                  <div className="flex items-center">
                    <MapPin className="h-3.5 w-3.5 mr-1.5 flex-shrink-0 text-gray-400" />
                    <span className="truncate">{location}</span>
                  </div>
                )}
                
                {company.contact_count !== undefined && (
                  <div className="flex items-center">
                    <Users className="h-3.5 w-3.5 mr-1.5 flex-shrink-0 text-gray-400" />
                    <span>{company.contact_count} contacts</span>
                  </div>
                )}
                
                {foundingYear && (
                  <div className="flex items-center">
                    <Calendar className="h-3.5 w-3.5 mr-1.5 flex-shrink-0 text-gray-400" />
                    <span>Founded {foundingYear}</span>
                  </div>
                )}
              </div>
            </div>
          </div>
          
          {/* Delete Button */}
          <div onClick={(e) => e.stopPropagation()}>
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="text-gray-400 hover:text-red-600 hover:bg-red-50 p-2 h-8 w-8"
                  disabled={isDeleting}
                >
                  {isDeleting ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Trash2 className="h-4 w-4" />
                  )}
                </Button>
              </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Delete Company</AlertDialogTitle>
                <AlertDialogDescription>
                  Are you sure you want to delete "{company.company_name}"? This action cannot be undone and will permanently remove:
                  <ul className="list-disc list-inside mt-2 space-y-1">
                    <li>Company overview and extracted data</li>
                    <li>Investment criteria records</li>
                    <li>Web crawler data (scraped pages)</li>
                    <li>All related processing data</li>
                  </ul>
                  <strong className="text-red-600 mt-2 block">
                    Note: Associated contacts will be preserved but their company association will be removed.
                  </strong>
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction
                  onClick={handleDeleteCompany}
                  className="bg-red-600 hover:bg-red-700"
                  disabled={isDeleting}
                >
                  {isDeleting ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Deleting...
                    </>
                  ) : (
                    'Delete Company'
                  )}
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
          </div>
        </div>
        
        {company.company_website && (
          <a 
            href={company.company_website.startsWith('http') ? company.company_website : `https://${company.company_website}`}
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center text-xs text-blue-600 hover:text-blue-800 mb-3 hover:underline underline-offset-2"
            onClick={(e) => e.stopPropagation()}
          >
            <Globe className="h-3.5 w-3.5 mr-1.5" />
            <span className="truncate">
              {company.company_website.replace(/^https?:\/\/(www\.)?/, '')}
            </span>
          </a>
        )}
        
        {/* Company Summary */}
        {company.summary && (
          <div className="mb-4 text-sm text-gray-600 line-clamp-2">
            {company.summary}
          </div>
        )}
        
        {/* Status Section */}
        <div className="flex flex-wrap gap-1.5 mb-3">
          {/* Company Overview V2 Status */}
          {(company as any).overview_v2_status && (
            <Badge 
              variant="outline" 
              className={cn("text-xs px-2 py-0.5 rounded-full", {
                "bg-green-50 text-green-700 border-green-200 hover:bg-green-100": (company as any).overview_v2_status === 'completed',
                "bg-amber-50 text-amber-700 border-amber-200 hover:bg-amber-100": (company as any).overview_v2_status === 'pending',
                "bg-red-50 text-red-700 border-red-200 hover:bg-red-100": (company as any).overview_v2_status === 'failed',
                "bg-gray-50 text-gray-700 border-gray-200 hover:bg-gray-100": !['completed', 'pending', 'failed'].includes((company as any).overview_v2_status)
              })}
            >
              Overview: {(company as any).overview_v2_status}
            </Badge>
          )}
          
          {/* Website Scraping Status */}
          {company.website_scraping_status && (
            <Badge 
              variant="outline" 
              className={cn("text-xs px-2 py-0.5 rounded-full", {
                "bg-green-50 text-green-700 border-green-200 hover:bg-green-100": company.website_scraping_status === 'completed',
                "bg-amber-50 text-amber-700 border-amber-200 hover:bg-amber-100": company.website_scraping_status === 'pending',
                "bg-red-50 text-red-700 border-red-200 hover:bg-red-100": company.website_scraping_status === 'failed',
                "bg-gray-50 text-gray-700 border-gray-200 hover:bg-gray-100": !['completed', 'pending', 'failed'].includes(company.website_scraping_status)
              })}
            >
              Scraping: {company.website_scraping_status}
            </Badge>
          )}
          
          {/* Investment Criteria Status */}
          {(company as any).investment_criteria_status && (
            <Badge 
              variant="outline" 
              className={cn("text-xs px-2 py-0.5 rounded-full", {
                "bg-green-50 text-green-700 border-green-200 hover:bg-green-100": (company as any).investment_criteria_status === 'completed',
                "bg-amber-50 text-amber-700 border-amber-200 hover:bg-amber-100": (company as any).investment_criteria_status === 'pending',
                "bg-red-50 text-red-700 border-red-200 hover:bg-red-100": (company as any).investment_criteria_status === 'failed',
                "bg-gray-50 text-gray-700 border-gray-200 hover:bg-gray-100": !['completed', 'pending', 'failed'].includes((company as any).investment_criteria_status)
              })}
            >
              IC: {(company as any).investment_criteria_status}
            </Badge>
          )}
        </div>

        {/* Tags Section */}
        <div className="space-y-2.5 mt-auto">
          {/* Property Types */}
          {company.propertytypes && company.propertytypes.length > 0 && (
            <div className="flex flex-wrap gap-1.5">
              {company.propertytypes.slice(0, 3).map((type, index) => (
                <Badge key={index} variant="outline" className="bg-green-50 text-green-700 border-green-200 hover:bg-green-100 text-xs px-2 py-0.5 rounded-full">
                  {type}
                </Badge>
              ))}
              {company.propertytypes.length > 3 && (
                <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 hover:bg-green-100 text-xs px-2 py-0.5 rounded-full">
                  +{company.propertytypes.length - 3}
                </Badge>
              )}
            </div>
          )}
          
          {/* Investment Focus */}
          {company.investmentfocus && company.investmentfocus.length > 0 && (
            <div className="flex flex-wrap gap-1.5">
              {company.investmentfocus.slice(0, 3).map((focus, index) => (
                <Badge key={index} variant="outline" className="bg-amber-50 text-amber-700 border-amber-200 hover:bg-amber-100 text-xs px-2 py-0.5 rounded-full">
                  {focus}
                </Badge>
              ))}
              {company.investmentfocus.length > 3 && (
                <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200 hover:bg-amber-100 text-xs px-2 py-0.5 rounded-full">
                  +{company.investmentfocus.length - 3}
                </Badge>
              )}
            </div>
          )}
          
          {/* Geographic Focus */}
          {company.geographicfocus && company.geographicfocus.length > 0 && (
            <div className="flex flex-wrap gap-1.5">
              {company.geographicfocus.slice(0, 3).map((region, index) => (
                <Badge key={index} variant="outline" className="bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100 text-xs px-2 py-0.5 rounded-full">
                  {region}
                </Badge>
              ))}
              {company.geographicfocus.length > 3 && (
                <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100 text-xs px-2 py-0.5 rounded-full">
                  +{company.geographicfocus.length - 3}
                </Badge>
              )}
            </div>
          )}
        </div>
      </div>
      
      <div className="px-5 py-3 bg-gray-50 border-t border-gray-100 flex justify-end">
        <Button 
          variant="ghost" 
          size="sm" 
          className="text-xs font-medium text-blue-600 hover:text-blue-700 hover:bg-blue-50 group flex items-center"
          onClick={(e: React.MouseEvent) => {
            e.stopPropagation()
            router.push(`/dashboard/companies/${company.company_id}`)
          }}
        >
          View Details
          <ArrowRight className="h-3.5 w-3.5 ml-1.5 transition-transform group-hover:translate-x-1" />
        </Button>
      </div>
    </div>
  )
} 