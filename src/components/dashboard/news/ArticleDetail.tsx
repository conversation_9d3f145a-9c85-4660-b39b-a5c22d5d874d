'use client'

import { useState, useEffect } from 'react'
import { 
  FileText, Building2, Calendar, MapPin, Globe, 
  ArrowLeft, User, Clock, CheckCircle, XCircle, 
  Tag, ExternalLink, Handshake, ChevronDown, 
  ChevronUp, DollarSign, Flag, BarChart3, Percent,
  TrendingUp, AlertCircle, Target, Zap, Play,
  Loader2, Settings, Database, Brain, Users,
  RefreshCw, Eye, Edit, Trash2
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useRouter } from 'next/navigation'

interface ArticleData {
  article: any
  properties: any[]
  market_metrics: any[]
  transactions: any[]
  entities: any[]
}

interface ArticleDetailProps {
  id: string
  onBack?: () => void
}

export default function ArticleDetail({ id, onBack }: ArticleDetailProps) {
  const router = useRouter()
  const [articleData, setArticleData] = useState<ArticleData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [processing, setProcessing] = useState<{[key: string]: boolean}>({})
  const [processingMessages, setProcessingMessages] = useState<{[key: string]: string}>({})
  
  // Collapsible sections state
  const [sections, setSections] = useState({
    overview: true,
    summary: true,
    content: true,
    properties: true,
    transactions: true,
    market_metrics: false,
    entities: false,
    processing: false,
    technical: false
  })

  // Toggle section visibility
  const toggleSection = (section: keyof typeof sections) => {
    setSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }))
  }

  useEffect(() => {
    if (id) {
      fetchArticleData()
    }
  }, [id])

  const fetchArticleData = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/articles/${id}`)
      
      if (!response.ok) {
        throw new Error(`Error ${response.status}: ${response.statusText}`)
      }
      
      const data = await response.json()
      setArticleData(data)
    } catch (err) {
      console.error('Error fetching article details:', err)
      setError('Failed to load article details. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleProcessingAction = async (action: string) => {
    if (!articleData?.article) return
    
    try {
      setProcessing(prev => ({ ...prev, [action]: true }))
      setProcessingMessages(prev => ({ ...prev, [action]: 'Starting...' }))
      
      if (action === 'full_process') {
        // For full process, run both stages sequentially
        setProcessingMessages(prev => ({ ...prev, [action]: 'Fetching content...' }))
        
        // First, fetch content
        const fetchResponse = await fetch(`${process.env.API_BASE_URL || ''}/api/processing/trigger`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ 
            action: 'execute_manual',
            stage: 'article_html_fetch',
            options: { singleId: parseInt(id), limit: 1 }
          })
        })
        
        const fetchResult = await fetchResponse.json()
        if (!fetchResponse.ok || !fetchResult.success) {
          throw new Error(`Fetch failed: ${fetchResult.error || 'Unknown error'}`)
        }
        
        // Wait a bit, then enrich
        setProcessingMessages(prev => ({ ...prev, [action]: 'Enriching content...' }))
        await new Promise(resolve => setTimeout(resolve, 3000))
        
        const enrichResponse = await fetch(`${process.env.API_BASE_URL || ''}/api/processing/trigger`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ 
            action: 'execute_manual',
            stage: 'article_enrichment',
            options: { singleId: parseInt(id), limit: 1 }
          })
        })
        
        const enrichResult = await enrichResponse.json()
        if (!enrichResponse.ok || !enrichResult.success) {
          throw new Error(`Enrichment failed: ${enrichResult.error || 'Unknown error'}`)
        }
        
        setProcessingMessages(prev => ({ ...prev, [action]: 'Full process completed successfully' }))
      } else {
        // For individual actions, use the appropriate stage
        const stage = action === 'fetch_content' ? 'article_html_fetch' : 
                     action === 'extract_data' || action === 'analyze_sentiment' ? 'article_enrichment' : 'article_html_fetch'
        
        const response = await fetch(`${process.env.API_BASE_URL || ''}/api/processing/trigger`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ 
            action: 'execute_manual',
            stage,
            options: { singleId: parseInt(id), limit: 1 }
          })
        })

        const result = await response.json()
        
        if (!response.ok || !result.success) {
          throw new Error(result.error || 'Processing failed')
        }
        
        setProcessingMessages(prev => ({ ...prev, [action]: 'Processing initiated successfully' }))
      }
      
      // Refresh article data after successful processing
      setTimeout(() => {
        fetchArticleData()
        setProcessingMessages(prev => ({ ...prev, [action]: '' }))
      }, 2000)
      
    } catch (error) {
      console.error(`Error during ${action}:`, error)
      setProcessingMessages(prev => ({ ...prev, [action]: `Error: ${error instanceof Error ? error.message : 'Processing failed'}` }))
    } finally {
      setProcessing(prev => ({ ...prev, [action]: false }))
    }
  }

  const handleMarkRelevant = async (isRelevant: boolean) => {
    try {
      const response = await fetch(`/api/articles/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          action: 'mark_relevant', 
          is_relevant: isRelevant 
        })
      })

      if (response.ok) {
        fetchArticleData()
      }
    } catch (error) {
      console.error('Error updating relevance:', error)
    }
  }

  const handleMarkBadUrl = async () => {
    try {
      const response = await fetch(`/api/articles/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'mark_bad_url' })
      })

      if (response.ok) {
        fetchArticleData()
      }
    } catch (error) {
      console.error('Error marking as bad URL:', error)
    }
  }

  // Format date
  const formatDate = (dateString: string) => {
    if (!dateString) return 'Unknown'
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  // Format date time
  const formatDateTime = (dateString: string) => {
    if (!dateString) return 'Unknown'
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // Get status color
  const getStatusColor = (status: string): string => {
    const colors: Record<string, string> = {
      'completed': 'bg-green-100 text-green-800',
      'processing': 'bg-blue-100 text-blue-800',
      'pending': 'bg-yellow-100 text-yellow-800',
      'failed': 'bg-red-100 text-red-800',
      'default': 'bg-gray-100 text-gray-800'
    }
    return colors[status?.toLowerCase()] || colors.default
  }

  // Section header component
  const SectionHeader = ({ title, icon, isOpen, onToggle, count }: { 
    title: string; 
    icon: React.ReactNode; 
    isOpen: boolean; 
    onToggle: () => void;
    count?: number;
  }) => (
    <div 
      className="flex items-center justify-between cursor-pointer py-3 px-4 bg-gray-50 hover:bg-gray-100 rounded-t-md transition-colors" 
      onClick={onToggle}
    >
      <div className="flex items-center">
        {icon}
        <h3 className="text-base font-medium text-gray-800 ml-2">
          {title} {count !== undefined && <span className="text-sm text-gray-500">({count})</span>}
        </h3>
      </div>
      {isOpen ? <ChevronUp className="h-4 w-4 text-gray-500" /> : <ChevronDown className="h-4 w-4 text-gray-500" />}
    </div>
  )

  if (loading) {
    return (
      <div className="bg-white rounded-lg border h-full overflow-y-auto">
        <div className="flex items-center justify-center p-8">
          <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </div>
    )
  }

  if (error || !articleData) {
    return (
      <div className="bg-white rounded-lg border h-full overflow-y-auto">
        <div className="flex flex-col items-center justify-center p-8">
          <FileText className="h-12 w-12 text-gray-300 mb-4" />
          <h3 className="text-base font-medium text-gray-900 mb-2">Article Not Found</h3>
          <p className="text-sm text-gray-500 mb-4">{error || 'Unable to load the requested article.'}</p>
          <Button 
            onClick={onBack || (() => router.back())}
            variant="outline"
            size="sm"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
        </div>
      </div>
    )
  }

  const { article, properties, market_metrics, transactions, entities } = articleData

  return (
    <div className="bg-white rounded-lg border h-full overflow-y-auto">
      {/* Top header */}
      <div className="border-b px-6 py-4 bg-gradient-to-r from-blue-50 to-indigo-50">
        <div className="flex justify-between items-start">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              <Button 
                variant="ghost" 
                size="sm"
                onClick={onBack || (() => router.back())}
                className="h-8 w-8 p-0 mr-2"
              >
                <ArrowLeft className="h-4 w-4" />
              </Button>
              <h1 className="text-xl font-bold text-gray-900 line-clamp-2">
                {article.headline || 'Untitled Article'}
              </h1>
            </div>
            
            <div className="flex flex-wrap gap-2 mb-3">
              {article.publication_name && (
                <Badge className="bg-blue-100 text-blue-800">
                  {article.publication_name}
                </Badge>
              )}
              
              <Badge className={getStatusColor(article.extraction_status)}>
                {article.extraction_status || 'pending'}
              </Badge>
              
              {article.sentiment && (
                <Badge className="bg-green-100 text-green-800">
                  {article.sentiment}
                </Badge>
              )}
              
              {article.is_distressed && (
                <Badge className="bg-red-100 text-red-800">
                  Distressed
                </Badge>
              )}
              
              {!article.is_relevant && (
                <Badge className="bg-gray-100 text-gray-800">
                  Not Relevant
                </Badge>
              )}
              
              {article.is_bad_url && (
                <Badge className="bg-orange-100 text-orange-800">
                  Bad URL
                </Badge>
              )}
            </div>
            
            <div className="flex items-center gap-4 text-sm text-gray-600">
              {article.author && (
                <div className="flex items-center">
                  <User className="h-4 w-4 mr-1" />
                  <span>{article.author}</span>
                </div>
              )}
              {article.publication_date && (
                <div className="flex items-center">
                  <Calendar className="h-4 w-4 mr-1" />
                  <span>{formatDate(article.publication_date)}</span>
                </div>
              )}
            </div>
          </div>
          
          <div className="flex items-center gap-2 ml-4">
            {article.article_url && (
              <Button 
                variant="ghost" 
                size="sm"
                onClick={() => window.open(article.article_url, '_blank')}
              >
                <ExternalLink className="h-4 w-4 mr-1" />
                View Original
              </Button>
            )}
          </div>
        </div>
      </div>
      
      <div className="divide-y">
        {/* Overview Section */}
        <div className="mb-2">
          <SectionHeader 
            title="Overview" 
            icon={<FileText className="h-4 w-4 text-blue-600" />} 
            isOpen={sections.overview} 
            onToggle={() => toggleSection('overview')} 
          />
          
          {sections.overview && (
            <div className="p-4">
              {article.summary && (
                <div className="mb-4">
                  <h4 className="text-sm font-medium text-gray-700 mb-2">Summary</h4>
                  <p className="text-sm text-gray-600 leading-relaxed">{article.summary}</p>
                </div>
              )}
              
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="bg-gray-50 p-3 rounded">
                  <p className="text-xs text-gray-500">Properties</p>
                  <p className="text-lg font-semibold text-gray-900">{properties.length}</p>
                </div>
                
                <div className="bg-gray-50 p-3 rounded">
                  <p className="text-xs text-gray-500">Transactions</p>
                  <p className="text-lg font-semibold text-gray-900">{transactions.length}</p>
                </div>
                
                <div className="bg-gray-50 p-3 rounded">
                  <p className="text-xs text-gray-500">Market Metrics</p>
                  <p className="text-lg font-semibold text-gray-900">{market_metrics.length}</p>
                </div>
                
                <div className="bg-gray-50 p-3 rounded">
                  <p className="text-xs text-gray-500">Entities</p>
                  <p className="text-lg font-semibold text-gray-900">{entities.length}</p>
                </div>
              </div>
              
              {/* Tags */}
              {article.llm_tags && Array.isArray(article.llm_tags) && article.llm_tags.length > 0 && (
                <div className="mt-4">
                  <h4 className="text-sm font-medium text-gray-700 mb-2">AI Tags</h4>
                  <div className="flex flex-wrap gap-2">
                    {article.llm_tags.map((tag: string, index: number) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        <Tag className="h-2 w-2 mr-1" />
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Processing Controls Section */}
        <div className="mb-2">
          <SectionHeader 
            title="Processing Controls" 
            icon={<Zap className="h-4 w-4 text-purple-600" />} 
            isOpen={sections.processing} 
            onToggle={() => toggleSection('processing')} 
          />
          
          {sections.processing && (
            <div className="p-4">
              <div className="grid grid-cols-2 md:grid-cols-2 gap-4 mb-4">
                {/* Fetch Content */}
                <div className="border rounded-lg p-3">
                  <h4 className="text-sm font-medium text-gray-900 mb-2">Fetch Content</h4>
                  <p className="text-xs text-gray-600 mb-3">Extract HTML content and clean text</p>
                  <Button
                    onClick={() => handleProcessingAction('fetch_content')}
                    disabled={processing.fetch_content}
                    size="sm"
                    className="w-full"
                  >
                    {processing.fetch_content ? (
                      <Loader2 className="h-3 w-3 animate-spin mr-1" />
                    ) : (
                      <Globe className="h-3 w-3 mr-1" />
                    )}
                    Fetch
                  </Button>
                  {processingMessages.fetch_content && (
                    <p className="text-xs text-blue-600 mt-2">{processingMessages.fetch_content}</p>
                  )}
                </div>

                {/* Extract Data */}
                <div className="border rounded-lg p-3">
                  <h4 className="text-sm font-medium text-gray-900 mb-2">Extract Data</h4>
                  <p className="text-xs text-gray-600 mb-3">AI-powered data extraction</p>
                  <Button
                    onClick={() => handleProcessingAction('extract_data')}
                    disabled={processing.extract_data}
                    size="sm"
                    className="w-full"
                  >
                    {processing.extract_data ? (
                      <Loader2 className="h-3 w-3 animate-spin mr-1" />
                    ) : (
                      <Database className="h-3 w-3 mr-1" />
                    )}
                    Extract
                  </Button>
                  {processingMessages.extract_data && (
                    <p className="text-xs text-blue-600 mt-2">{processingMessages.extract_data}</p>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Article Summary Section */}
        <div className="mb-2">
          <SectionHeader 
            title="Summary" 
            icon={<BarChart3 className="h-4 w-4 text-blue-600" />} 
            isOpen={sections.summary} 
            onToggle={() => toggleSection('summary')} 
          />
          
          {sections.summary && (
            <div className="p-4">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center p-3 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">{entities.length}</div>
                  <div className="text-sm text-gray-600">Entities</div>
                </div>
                <div className="text-center p-3 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">{properties.length}</div>
                  <div className="text-sm text-gray-600">Properties</div>
                </div>
                <div className="text-center p-3 bg-purple-50 rounded-lg">
                  <div className="text-2xl font-bold text-purple-600">{transactions.length}</div>
                  <div className="text-sm text-gray-600">Transactions</div>
                </div>
                <div className="text-center p-3 bg-orange-50 rounded-lg">
                  <div className="text-2xl font-bold text-orange-600">{market_metrics.length}</div>
                  <div className="text-sm text-gray-600">Market Metrics</div>
                </div>
              </div>
              
                                    {/* Entity Type Breakdown */}
                      {entities.length > 0 && (
                        <div className="mt-4">
                          <h5 className="text-sm font-medium text-gray-700 mb-2">Entity Breakdown:</h5>
                          <div className="flex flex-wrap gap-2">
                            {(() => {
                              const entityTypeCounts = entities.reduce((acc, entity) => {
                                const type = entity.entity_type || 'Unknown';
                                acc[type] = (acc[type] || 0) + 1;
                                return acc;
                              }, {} as Record<string, number>);
          
                              return Object.entries(entityTypeCounts).map(([type, count]) => (
                                <Badge key={type} variant="outline" className="text-xs">
                                  {type}: {count as number}
                                </Badge>
                              ));
                            })()}
                          </div>
                          
                          {/* Enhanced Entity Statistics */}
                          <div className="mt-3 pt-3 border-t border-gray-200">
                            <div className="grid grid-cols-2 gap-4 text-xs">
                              <div>
                                <span className="font-medium text-gray-700">Verified Entities:</span>
                                <span className="ml-2 text-gray-600">
                                  {entities.filter(e => e.web_search_verified).length} / {entities.length}
                                </span>
                              </div>
                              <div>
                                <span className="font-medium text-gray-700">High Confidence:</span>
                                <span className="ml-2 text-gray-600">
                                  {entities.filter(e => e.confidence && e.confidence >= 0.8).length} / {entities.length}
                                </span>
                              </div>
                              <div>
                                <span className="font-medium text-gray-700">With Descriptions:</span>
                                <span className="ml-2 text-gray-600">
                                  {entities.filter(e => e.entity_description).length} / {entities.length}
                                </span>
                              </div>
                              <div>
                                <span className="font-medium text-gray-700">With Context:</span>
                                <span className="ml-2 text-gray-600">
                                  {entities.filter(e => e.additional_context).length} / {entities.length}
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Data Quality Summary */}
                      <div className="mt-4 pt-4 border-t border-gray-200">
                        <h5 className="text-sm font-medium text-gray-700 mb-2">Data Quality:</h5>
                        <div className="flex flex-wrap gap-2">
                          {(() => {
                            const qualityIndicators: React.ReactNode[] = [];
                            
                            // Check for placeholder values in properties
                            const hasPlaceholderProperties = properties.some(p => 
                              (p.city && p.city.length <= 1) || 
                              (p.state && p.state.length <= 1) || 
                              (p.region && p.region.length <= 1) || 
                              (p.country && p.country.length <= 1)
                            );
                            
                            if (hasPlaceholderProperties) {
                              qualityIndicators.push(
                                <Badge key="properties" variant="outline" className="text-xs bg-yellow-50 text-yellow-700 border-yellow-200">
                                  Properties: Some location data incomplete
                                </Badge>
                              );
                            }
                            
                            // Check for limited transaction details
                            const hasLimitedTransactions = transactions.some(t => 
                              !t.cap_rate && !t.price_per_sf && !t.loan_type && !t.equity_type
                            );
                            
                            if (hasLimitedTransactions) {
                              qualityIndicators.push(
                                <Badge key="transactions" variant="outline" className="text-xs bg-yellow-50 text-yellow-700 border-yellow-200">
                                  Transactions: Some details missing
                                </Badge>
                              );
                            }
                            
                            // Check for limited market metrics
                            const hasLimitedMetrics = market_metrics.some(m => 
                              !m.vacancy_rate && !m.rental_rate && !m.transaction_volume && !m.construction_pipeline
                            );
                            
                            if (hasLimitedMetrics) {
                              qualityIndicators.push(
                                <Badge key="metrics" variant="outline" className="text-xs bg-yellow-50 text-yellow-700 border-yellow-200">
                                  Market Metrics: Limited data available
                                </Badge>
                              );
                            }
                            
                            // Check for entity quality
                            const hasLowConfidenceEntities = entities.some(e => e.confidence && e.confidence < 0.6);
                            const hasUnverifiedEntities = entities.some(e => !e.web_search_verified);
                            
                            if (hasLowConfidenceEntities) {
                              qualityIndicators.push(
                                <Badge key="entities-confidence" variant="outline" className="text-xs bg-yellow-50 text-yellow-700 border-yellow-200">
                                  Entities: Some low confidence
                                </Badge>
                              );
                            }
                            
                            if (hasUnverifiedEntities) {
                              qualityIndicators.push(
                                <Badge key="entities-verified" variant="outline" className="text-xs bg-blue-50 text-blue-700 border-blue-200">
                                  Entities: Some unverified
                                </Badge>
                              );
                            }
                            
                            if (qualityIndicators.length === 0) {
                              qualityIndicators.push(
                                <Badge key="good" variant="outline" className="text-xs bg-green-50 text-green-700 border-green-200">
                                  All data complete
                                </Badge>
                              );
                            }
                            
                            return qualityIndicators;
                          })()}
                        </div>
                      </div>
            </div>
          )}
        </div>

        {/* Content Section */}
        <div className="mb-2">
          <SectionHeader 
            title="Content" 
            icon={<FileText className="h-4 w-4 text-blue-600" />} 
            isOpen={sections.content} 
            onToggle={() => toggleSection('content')} 
          />
          
          {sections.content && (
            <div className="p-4">
              {article.article_body_text ? (
                <div className="bg-gray-50 p-4 rounded-md max-h-[400px] overflow-y-auto">
                  <div className="prose prose-sm max-w-none">
                    {article.article_body_text.split('\n').map((paragraph: string, idx: number) => (
                      <p key={idx} className="text-sm mb-2">{paragraph}</p>
                    ))}
                  </div>
                </div>
              ) : (
                <p className="text-sm text-gray-500 italic">No content available</p>
              )}
            </div>
          )}
        </div>

        {/* Properties Section */}
        <div className="mb-2">
          <SectionHeader 
            title="Properties" 
            icon={<Building2 className="h-4 w-4 text-green-600" />} 
            isOpen={sections.properties} 
            onToggle={() => toggleSection('properties')} 
            count={properties.length}
          />
          
          {sections.properties && (
            <div className="p-4">
              {properties.length === 0 ? (
                <p className="text-sm text-gray-500">No properties extracted</p>
              ) : (
                <div className="space-y-4">
                  {properties.map((property) => (
                    <div key={property.article_property_id} className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                      <div className="space-y-3">
                        {/* Property Name and Address */}
                        <div>
                          <h4 className="font-medium text-gray-900 text-lg">
                            {property.property_name || 'Unnamed Property'}
                          </h4>
                          {property.address && (
                            <p className="text-sm text-gray-600 flex items-center gap-1 mt-1">
                              <MapPin className="h-3 w-3" />
                              {property.address}
                            </p>
                          )}
                        </div>

                        {/* Location Info */}
                        <div className="flex flex-wrap gap-2">
                          {property.city && property.city.length > 1 && (
                            <Badge variant="outline" className="text-xs">
                              {property.city}
                            </Badge>
                          )}
                          {property.state && property.state.length > 1 && (
                            <Badge variant="outline" className="text-xs">
                              {property.state}
                            </Badge>
                          )}
                          {property.region && property.region.length > 1 && (
                            <Badge variant="outline" className="text-xs">
                              {property.region}
                            </Badge>
                          )}
                          {property.country && property.country.length > 1 && (
                            <Badge variant="outline" className="text-xs">
                              {property.country}
                            </Badge>
                          )}
                          {/* Show placeholder warning if location data is incomplete */}
                          {((property.city && property.city.length <= 1) || 
                            (property.state && property.state.length <= 1) || 
                            (property.region && property.region.length <= 1) || 
                            (property.country && property.country.length <= 1)) && (
                            <Badge variant="outline" className="text-xs bg-yellow-50 text-yellow-700 border-yellow-200">
                              Location data incomplete
                            </Badge>
                          )}
                        </div>

                        {/* Property Details */}
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                          {property.construction_type && (
                            <div className="text-sm">
                              <span className="text-gray-500">Type:</span>
                              <div className="font-medium">{property.construction_type}</div>
                            </div>
                          )}
                          {property.unit_count && (
                            <div className="text-sm">
                              <span className="text-gray-500">Units:</span>
                              <div className="font-medium">{property.unit_count.toLocaleString()}</div>
                            </div>
                          )}
                          {property.square_footage && (
                            <div className="text-sm">
                              <span className="text-gray-500">Size:</span>
                              <div className="font-medium">{typeof property.square_footage === 'string' ? property.square_footage : property.square_footage.toLocaleString()} SF</div>
                            </div>
                          )}
                          {property.job_creation && (
                            <div className="text-sm">
                              <span className="text-gray-500">Jobs:</span>
                              <div className="font-medium">{property.job_creation.toLocaleString()}</div>
                            </div>
                          )}
                          {property.zipcode && (
                            <div className="text-sm">
                              <span className="text-gray-500">ZIP:</span>
                              <div className="font-medium">{property.zipcode}</div>
                            </div>
                          )}
                        </div>

                        {/* Timeline and Additional Info */}
                        {(property.project_timeline || property.subsidy_info) && (
                          <div className="space-y-2">
                            {property.project_timeline && (
                              <div className="text-sm">
                                <span className="text-gray-500">Timeline:</span>
                                <div className="font-medium">{property.project_timeline}</div>
                              </div>
                            )}
                            {property.subsidy_info && (
                              <div className="text-sm">
                                <span className="text-gray-500">Subsidies:</span>
                                <div className="font-medium">{property.subsidy_info}</div>
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>

        {/* Transactions Section */}
        <div className="mb-2">
          <SectionHeader 
            title="Transactions" 
            icon={<Handshake className="h-4 w-4 text-purple-600" />} 
            isOpen={sections.transactions} 
            onToggle={() => toggleSection('transactions')} 
            count={transactions.length}
          />
          
          {sections.transactions && (
            <div className="p-4">
              {transactions.length === 0 ? (
                <p className="text-sm text-gray-500">No transactions extracted</p>
              ) : (
                <div className="space-y-4">
                  {transactions.map((transaction) => (
                    <div key={transaction.article_transaction_id} className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                      <div className="space-y-3">
                        {/* Deal Header */}
                        <div className="flex items-center justify-between">
                          <h4 className="font-medium text-gray-900 text-lg">{transaction.deal_type}</h4>
                          <div className="flex items-center gap-2">
                            {transaction.deal_size && (
                              <Badge className="bg-green-100 text-green-800">{transaction.deal_size}</Badge>
                            )}
                            {/* Show data quality indicator if key fields are missing */}
                            {(!transaction.cap_rate && !transaction.price_per_sf && !transaction.loan_type && !transaction.equity_type) && (
                              <Badge variant="outline" className="text-xs bg-yellow-50 text-yellow-700 border-yellow-200">
                                Limited details
                              </Badge>
                            )}
                          </div>
                        </div>

                        {/* Deal Details */}
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                          {transaction.cap_rate && (
                            <div className="text-sm">
                              <span className="text-gray-500">Cap Rate:</span>
                              <div className="font-medium">{transaction.cap_rate}</div>
                            </div>
                          )}
                          {transaction.price_per_sf && (
                            <div className="text-sm">
                              <span className="text-gray-500">Price/SF:</span>
                              <div className="font-medium">${typeof transaction.price_per_sf === 'string' ? transaction.price_per_sf : transaction.price_per_sf.toLocaleString()}</div>
                            </div>
                          )}
                          {transaction.loan_type && (
                            <div className="text-sm">
                              <span className="text-gray-500">Loan Type:</span>
                              <div className="font-medium">{transaction.loan_type}</div>
                            </div>
                          )}
                          {transaction.equity_type && (
                            <div className="text-sm">
                              <span className="text-gray-500">Equity Type:</span>
                              <div className="font-medium">{transaction.equity_type}</div>
                            </div>
                          )}
                        </div>

                        {/* Financing Types */}
                        {transaction.financing_type && (
                          <div className="flex flex-wrap gap-2">
                            {Array.isArray(transaction.financing_type) ? (
                              transaction.financing_type.map((type: string, index: number) => (
                                <Badge key={index} variant="outline" className="text-xs">
                                  {type}
                                </Badge>
                              ))
                            ) : (
                              <Badge variant="outline" className="text-xs">
                                {transaction.financing_type}
                              </Badge>
                            )}1
                          </div>
                        )}
                        
                        {/* Capital Position */}
                        {transaction.capital_position && (
                          <div className="flex flex-wrap gap-2">
                            {Array.isArray(transaction.capital_position) ? (
                              transaction.capital_position.map((position: string, index: number) => (
                                <Badge key={index} variant="outline" className="text-xs">
                                  {position}
                                </Badge>
                              ))
                            ) : (
                              <Badge variant="outline" className="text-xs">
                                {transaction.capital_position}
                              </Badge>
                            )}
                          </div>
                        )}

                        {/* property types */}
                        {transaction.property_types && (
                          <div className="flex flex-wrap gap-2">
                            {Array.isArray(transaction.property_types) ? (
                              transaction.property_types.map((type: string, index: number) => (
                                <Badge key={index} variant="outline" className="text-xs">
                                  {type}
                                </Badge>
                              ))
                            ) : (
                              <Badge variant="outline" className="text-xs">
                                {transaction.property_types}
                              </Badge>
                            )}
                          </div>
                        )}

                        {/* Capital Stack Notes */}
                        {transaction.capital_stack_notes && (
                          <div className="space-y-2">
                            <span className="text-gray-500 text-xs font-medium">Capital Stack Details:</span>
                            <div className="space-y-1">
                              {Array.isArray(transaction.capital_stack_notes) ? (
                                transaction.capital_stack_notes.map((note: string, index: number) => (
                                  <p key={index} className="text-sm text-gray-700 bg-white p-2 rounded border">
                                    {note}
                                  </p>
                                ))
                              ) : (
                                <p className="text-sm text-gray-700 bg-white p-2 rounded border">
                                  {transaction.capital_stack_notes}
                                </p>
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>

        {/* Market Metrics Section */}
        <div className="mb-2">
          <SectionHeader 
            title="Market Metrics" 
            icon={<BarChart3 className="h-4 w-4 text-orange-600" />} 
            isOpen={sections.market_metrics} 
            onToggle={() => toggleSection('market_metrics')} 
            count={market_metrics.length}
          />
          
          {sections.market_metrics && (
            <div className="p-4">
              {market_metrics.length === 0 ? (
                <p className="text-sm text-gray-500">No market metrics extracted</p>
              ) : (
                <div className="space-y-4">
                  {market_metrics.map((metric) => (
                    <div key={metric.article_market_metric_id} className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                      <div className="space-y-3">
                        {/* Market Header */}
                        <div className="flex items-center justify-between">
                          <div>
                            <h4 className="font-medium text-gray-900 text-lg">
                              {metric.market_city && metric.market_state ? 
                                `${metric.market_city}, ${metric.market_state}` : 
                                'Market Data'
                              }
                            </h4>
                            {metric.time_period && (
                              <p className="text-sm text-gray-600">{metric.time_period}</p>
                            )}
                          </div>
                          {/* Show data quality indicator if key market data is missing */}
                          {(!metric.vacancy_rate && !metric.rental_rate && !metric.transaction_volume && !metric.construction_pipeline) && (
                            <Badge variant="outline" className="text-xs bg-yellow-50 text-yellow-700 border-yellow-200">
                              Limited metrics
                            </Badge>
                          )}
                        </div>

                        {/* Market Metrics Grid */}
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                          {metric.vacancy_rate && (
                            <div className="text-sm">
                              <span className="text-gray-500">Vacancy Rate:</span>
                              <div className="font-medium">{(typeof metric.vacancy_rate === 'number' ? metric.vacancy_rate * 100 : parseFloat(metric.vacancy_rate) * 100).toFixed(1)}%</div>
                            </div>
                          )}
                          {metric.rental_rate && (
                            <div className="text-sm">
                              <span className="text-gray-500">Rental Rate:</span>
                              <div className="font-medium">${typeof metric.rental_rate === 'number' ? metric.rental_rate.toLocaleString() : metric.rental_rate}</div>
                            </div>
                          )}
                          {metric.absorption_rate && (
                            <div className="text-sm">
                              <span className="text-gray-500">Absorption:</span>
                              <div className="font-medium">{typeof metric.absorption_rate === 'number' ? metric.absorption_rate.toLocaleString() : metric.absorption_rate} SF</div>
                            </div>
                          )}
                          {metric.cap_rate_avg && (
                            <div className="text-sm">
                              <span className="text-gray-500">Avg Cap Rate:</span>
                              <div className="font-medium">{(typeof metric.cap_rate_avg === 'number' ? metric.cap_rate_avg * 100 : parseFloat(metric.cap_rate_avg) * 100).toFixed(1)}%</div>
                            </div>
                          )}
                          {metric.transaction_volume && (
                            <div className="text-sm">
                              <span className="text-gray-500">Transaction Volume:</span>
                              <div className="font-medium">${(typeof metric.transaction_volume === 'number' ? metric.transaction_volume / 1000000 : parseFloat(metric.transaction_volume) / 1000000).toFixed(0)}M</div>
                            </div>
                          )}
                          {metric.construction_pipeline && (
                            <div className="text-sm">
                              <span className="text-gray-500">Construction Pipeline:</span>
                              <div className="font-medium">${(typeof metric.construction_pipeline === 'number' ? metric.construction_pipeline / 1000000 : parseFloat(metric.construction_pipeline) / 1000000).toFixed(0)}M</div>
                            </div>
                          )}
                          {metric.new_deliveries_sf && (
                            <div className="text-sm">
                              <span className="text-gray-500">New Deliveries:</span>
                              <div className="font-medium">{typeof metric.new_deliveries_sf === 'number' ? metric.new_deliveries_sf.toLocaleString() : metric.new_deliveries_sf} SF</div>
                            </div>
                          )}
                        </div>

                        {/* Market Trends */}
                        {(metric.rental_rate_trend || metric.cap_rate_trend || metric.demand_trend) && (
                          <div className="flex flex-wrap gap-2">
                            {metric.rental_rate_trend && (
                              <Badge variant="outline" className="text-xs">
                                Rent: {metric.rental_rate_trend}
                              </Badge>
                            )}
                            {metric.cap_rate_trend && (
                              <Badge variant="outline" className="text-xs">
                                Cap Rate: {metric.cap_rate_trend}
                              </Badge>
                            )}
                            {metric.demand_trend && (
                              <Badge variant="outline" className="text-xs">
                                Demand: {metric.demand_trend}
                              </Badge>
                            )}
                          </div>
                        )}

                        {/* Commentary and Additional Info */}
                        {(metric.commentary || metric.remote_work_impact) && (
                          <div className="space-y-2">
                            {metric.commentary && (
                              <div>
                                <span className="text-gray-500 text-xs font-medium">Market Commentary:</span>
                                <p className="text-sm text-gray-700 mt-1">{metric.commentary}</p>
                              </div>
                            )}
                            {metric.remote_work_impact && (
                              <div>
                                <span className="text-gray-500 text-xs font-medium">Remote Work Impact:</span>
                                <p className="text-sm text-gray-700 mt-1">{metric.remote_work_impact}</p>
                              </div>
                            )}
                          </div>
                        )}

                        {/* Distress Indicator */}
                        {metric.distress_indicator !== null && (
                          <div className="flex items-center gap-2">
                            <span className="text-gray-500 text-xs">Distress Indicator:</span>
                            <Badge variant={metric.distress_indicator ? "destructive" : "outline"} className="text-xs">
                              {metric.distress_indicator ? "Yes" : "No"}
                            </Badge>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>

        {/* Entities Section */}
        <div className="mb-2">
          <SectionHeader 
            title="Entities" 
            icon={<Users className="h-4 w-4 text-indigo-600" />} 
            isOpen={sections.entities} 
            onToggle={() => toggleSection('entities')} 
            count={entities.length}
          />
          
          {sections.entities && (
            <div className="p-4">
              {entities.length === 0 ? (
                <p className="text-sm text-gray-500">No entities extracted</p>
              ) : (
                <div className="space-y-4">
                  {/* Group entities by entity type for better organization */}
                  {(() => {
                    const groupedEntities = entities.reduce((acc, entity) => {
                      const entityType = entity.entity_type || 'Unknown Type';
                      if (!acc[entityType]) {
                        acc[entityType] = [];
                      }
                      acc[entityType].push(entity);
                      return acc;
                    }, {} as Record<string, any[]>);

                    return Object.entries(groupedEntities).map(([entityType, typeEntities]) => (
                      <div key={entityType} className="space-y-3">
                        <h4 className="text-sm font-medium text-gray-700 flex items-center gap-2">
                          <Badge className="bg-indigo-100 text-indigo-800 text-xs capitalize">
                            {entityType}
                          </Badge>
                          <span>({(typeEntities as any[]).length})</span>
                        </h4>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                          {(typeEntities as any[]).map((entity) => (
                                  <div key={entity.article_entity_id} className="bg-gray-50 p-3 rounded-lg border border-gray-200">
                                    <div className="flex items-start justify-between">
                                      <div className="flex-1">
                                        <div className="flex items-center gap-2 mb-1">
                                          <h6 className="font-medium text-gray-900 text-sm">
                                            {entity.entity_name}
                                          </h6>
                                          {entity.entity_role && (
                                            <Badge variant="outline" className="text-xs">
                                              {entity.entity_role}
                                            </Badge>
                                          )}
                                        </div>
                                        
                                        {/* Entity Description */}
                                        {entity.entity_description && (
                                          <p className="text-xs text-gray-600 mt-1">
                                            {entity.entity_description}
                                          </p>
                                        )}
                                        
                                        {/* Entity Data - Show key information from entityData */}
                                        {entity.entity_data && (
                                          <div className="mt-2 space-y-1">
                                            {(() => {
                                              const entityData = typeof entity.entity_data === 'string' 
                                                ? JSON.parse(entity.entity_data) 
                                                : entity.entity_data;
                                              
                                              const displayItems: React.ReactNode[] = [];
                                              
                                              // For contacts, show email, LinkedIn, company, title
                                              if (entity.entity_type === 'contact') {
                                                if (entityData.email) {
                                                  displayItems.push(
                                                    <div key="email" className="flex items-center gap-1 text-xs text-gray-600">
                                                      <span className="font-medium">Email:</span>
                                                      <a href={`mailto:${entityData.email}`} className="text-blue-600 hover:underline">
                                                        {entityData.email}
                                                      </a>
                                                    </div>
                                                  );
                                                }
                                                if (entityData.linkedin_url) {
                                                  displayItems.push(
                                                    <div key="linkedin" className="flex items-center gap-1 text-xs text-gray-600">
                                                      <span className="font-medium">LinkedIn:</span>
                                                      <a href={entityData.linkedin_url} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                                                        View Profile
                                                      </a>
                                                    </div>
                                                  );
                                                }
                                                if (entityData.company) {
                                                  displayItems.push(
                                                    <div key="company" className="flex items-center gap-1 text-xs text-gray-600">
                                                      <span className="font-medium">Company:</span>
                                                      <span>{entityData.company}</span>
                                                    </div>
                                                  );
                                                }
                                                if (entityData.title) {
                                                  displayItems.push(
                                                    <div key="title" className="flex items-center gap-1 text-xs text-gray-600">
                                                      <span className="font-medium">Title:</span>
                                                      <span>{entityData.title}</span>
                                                    </div>
                                                  );
                                                }
                                              }
                                              
                                              // For companies, show website, industry, headquarters
                                              if (entity.entity_type === 'company') {
                                                if (entityData.website) {
                                                  displayItems.push(
                                                    <div key="website" className="flex items-center gap-1 text-xs text-gray-600">
                                                      <span className="font-medium">Website:</span>
                                                      <a href={entityData.website} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                                                        Visit Site
                                                      </a>
                                                    </div>
                                                  );
                                                }
                                                if (entityData.industry) {
                                                  displayItems.push(
                                                    <div key="industry" className="flex items-center gap-1 text-xs text-gray-600">
                                                      <span className="font-medium">Industry:</span>
                                                      <span>{entityData.industry}</span>
                                                    </div>
                                                  );
                                                }
                                                if (entityData.headquarters) {
                                                  displayItems.push(
                                                    <div key="headquarters" className="flex items-center gap-1 text-xs text-gray-600">
                                                      <span className="font-medium">HQ:</span>
                                                      <span>{entityData.headquarters}</span>
                                                    </div>
                                                  );
                                                }
                                                if (entityData.company_type) {
                                                  displayItems.push(
                                                    <div key="type" className="flex items-center gap-1 text-xs text-gray-600">
                                                      <span className="font-medium">Type:</span>
                                                      <span>{entityData.company_type}</span>
                                                    </div>
                                                  );
                                                }
                                              }
                                              
                                              return displayItems;
                                            })()}
                                          </div>
                                        )}
                                        
                                        {/* Additional Context */}
                                        {entity.additional_context && (
                                          <p className="text-xs text-gray-500 mt-1 italic">
                                            {entity.additional_context}
                                          </p>
                                        )}
                                      </div>
                                      <div className="flex items-center gap-1">
                                        {/* View Entity Details Button */}
                                        {entity.entity_id && (
                                          <Button
                                            variant="ghost"
                                            size="sm"
                                            className="h-6 w-6 p-0"
                                            title={`View ${entity.entity_type} details`}
                                            onClick={() => {
                                              if (entity.entity_type === 'contact') {
                                                window.open(`/dashboard/people/${entity.entity_id}`, '_blank')
                                              } else if (entity.entity_type === 'company') {
                                                window.open(`/dashboard/companies/${entity.entity_id}`, '_blank')
                                              }
                                            }}
                                          >
                                            <Eye className="h-3 w-3" />
                                          </Button>
                                        )}
                                        
                                        {/* External Links for Contact/Company */}
                                        {entity.entity_data && (() => {
                                          const entityData = typeof entity.entity_data === 'string' 
                                            ? JSON.parse(entity.entity_data) 
                                            : entity.entity_data;
                                          
                                          if (entity.entity_type === 'contact' && entityData.linkedin_url) {
                                            return (
                                              <Button
                                                variant="ghost"
                                                size="sm"
                                                className="h-6 w-6 p-0"
                                                title="View LinkedIn profile"
                                                onClick={() => window.open(entityData.linkedin_url, '_blank')}
                                              >
                                                <ExternalLink className="h-3 w-3" />
                                              </Button>
                                            );
                                          }
                                          
                                          if (entity.entity_type === 'company' && entityData.website) {
                                            return (
                                              <Button
                                                variant="ghost"
                                                size="sm"
                                                className="h-6 w-6 p-0"
                                                title="Visit company website"
                                                onClick={() => window.open(entityData.website, '_blank')}
                                              >
                                                <Globe className="h-3 w-3" />
                                              </Button>
                                            );
                                          }
                                          
                                          return null;
                                        })()}
                                      </div>
                                    </div>
                                  </div>
                          ))}
                        </div>
                      </div>
                    ));
                  })()}
                </div>
              )}
            </div>
          )}
        </div>

        {/* Actions Section */}
        <div className="border-t">
          <SectionHeader 
            title="Actions" 
            icon={<Settings className="h-4 w-4 text-gray-600" />} 
            isOpen={true}
            onToggle={() => {}} 
          />
          
          <div className="p-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              <Button
                onClick={() => handleMarkRelevant(!article.is_relevant)}
                variant="outline"
                size="sm"
                className="w-full"
              >
                {article.is_relevant ? (
                  <XCircle className="h-4 w-4 mr-1" />
                ) : (
                  <CheckCircle className="h-4 w-4 mr-1" />
                )}
                Mark {article.is_relevant ? 'Not Relevant' : 'Relevant'}
              </Button>
              
              <Button
                onClick={handleMarkBadUrl}
                disabled={article.is_bad_url}
                variant="outline"
                size="sm"
                className="w-full"
              >
                <Flag className="h-4 w-4 mr-1" />
                {article.is_bad_url ? 'Bad URL' : 'Mark Bad URL'}
              </Button>
              
              <Button
                onClick={() => window.open(`/dashboard/articles/${id}/edit`, '_blank')}
                variant="outline"
                size="sm"
                className="w-full"
              >
                <Edit className="h-4 w-4 mr-1" />
                Edit
              </Button>
              
              <Button
                onClick={fetchArticleData}
                variant="outline"
                size="sm"
                className="w-full"
              >
                <RefreshCw className="h-4 w-4 mr-1" />
                Refresh
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
