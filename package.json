{"name": "dashboard", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev -p 3030", "build": "cross-env NODE_ENV=production  NODE_OPTIONS=--enable-source-maps next build", "prod-server": "cross-env NODE_ENV=production next build && next start -p 3030", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "migrate:deals-investment-criteria": "tsx scripts/migrate_deals_and_investment_criteria.ts", "migrate:local": "tsx scripts/run-sql-migrations.ts", "migrate:typeorm": "tsx --tsconfig tsconfig.typeorm.json scripts/run-migrations.ts", "migrate:typeorm:run": "tsx --tsconfig tsconfig.typeorm.json scripts/run-migrations.ts", "migrate:typeorm:revert": "tsx --tsconfig tsconfig.typeorm.json scripts/revert-migrations.ts", "migrate:typeorm:simple": "tsx scripts/run-migrations-simple.ts", "migrate:typeorm:generate": "tsx scripts/generate-migration.ts", "migrate:typeorm:auto": "tsx scripts/auto-migrate.ts", "migrate:typeorm:auto-generate": "tsx scripts/auto-migrate.ts --generate", "typeorm": "tsx ./node_modules/typeorm/cli.js -d src/lib/typeorm/config-cli.cjs", "typeorm:migration:generate": "npm run typeorm -- migration:generate", "typeorm:migration:run": "npm run typeorm -- migration:run", "typeorm:migration:revert": "npm run typeorm -- migration:revert", "typeorm:schema:sync": "npm run typeorm -- schema:sync", "typeorm:schema:drop": "npm run typeorm -- schema:drop", "start:all:dev": "concurrently -k -n \"<PERSON><PERSON><PERSON><PERSON>,G<PERSON>Worker,FirefliesWorker,BullBoard\" -c \"magenta,green,yellow,blue\" \"npx tsx scripts/deal-worker.ts\" \"npx tsx scripts/gmail-worker.ts\" \"npx tsx scripts/fireflies-worker.ts\" \"npx tsx scripts/bull-board-server.ts\"", "start:all:prod": "concurrently -k -n \"<PERSON><PERSON><PERSON><PERSON>,GmailWorker,FirefliesWorker,BullBoard\" -c \"magenta,green,yellow,blue\" \"node dist/scripts/deal-worker.js\" \"node dist/scripts/gmail-worker.js\" \"node dist/scripts/fireflies-worker.js\" \"node dist/scripts/bull-board-server.js\"", "typeorm:helper": "tsx scripts/migration-helper.ts", "bullboard": "tsx scripts/bull-board-server.ts", "cleanup:bull": "tsx scripts/cleanup-bull-jobs.ts", "cleanup:db": "tsx scripts/cleanup-job-database.ts", "cleanup:all": "tsx scripts/cleanup-bull-jobs.ts all && tsx scripts/cleanup-job-database.ts all", "auth:create-test-users": "tsx scripts/create-test-admin.ts", "start:fireflies:dev": "npx tsx scripts/fireflies-worker.ts", "start:fireflies:prod": "node dist/scripts/fireflies-worker.js", "worker:job-queue": "tsx scripts/job-queue-worker.ts", "duplicates:clear": "tsx scripts/clear-duplicates.ts", "duplicates:refresh": "tsx scripts/refresh-duplicate-analysis.ts"}, "dependencies": {"@bull-board/api": "^6.11.0", "@bull-board/express": "^6.11.0", "@google/generative-ai": "^0.24.1", "@hookform/resolvers": "^3.10.0", "@livekit/components-react": "^2.9.2", "@livekit/components-styles": "^1.1.5", "@radix-ui/react-alert-dialog": "^1.1.7", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-progress": "^1.1.3", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-slider": "^1.2.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-toast": "^1.2.7", "@tinymce/tinymce-react": "^6.1.0", "@types/formidable": "^3.4.5", "@types/node-fetch": "^2.6.12", "@types/papaparse": "^5.3.16", "@types/pg": "^8.11.13", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "bottleneck": "^2.19.5", "bull": "^4.16.5", "bullmq": "^5.56.1", "calendar": "^0.1.1", "cheerio": "^1.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "csv-parse": "^5.6.0", "csv-writer": "^1.6.0", "date-fns": "^4.1.0", "exceljs": "^4.4.0", "formidable": "^3.5.4", "framer-motion": "^12.18.1", "google-auth-library": "^10.1.0", "googleapis": "^150.0.1", "ioredis": "^5.6.1", "json2csv": "^6.0.0-alpha.2", "langsmith": "^0.3.33", "livekit-client": "^2.11.2", "livekit-server-sdk": "^2.12.0", "lodash": "^4.17.21", "lucide-react": "^0.454.0", "mammoth": "^1.9.1", "mermaid": "^11.6.0", "next": "^15.3.5", "next-auth": "^4.24.11", "next-connect": "^1.0.0", "node-cache": "^5.1.2", "nodemailer": "^6.10.1", "openai": "^4.95.0", "p-limit": "^6.2.0", "papaparse": "^5.5.3", "pdf-parse": "^1.1.1", "pg": "^8.16.3", "playwright": "^1.52.0", "puppeteer": "^23.11.1", "react": "^18.3.1", "react-datasheet-grid": "^4.11.5", "react-day-picker": "^9.6.7", "react-dom": "^18.3.1", "react-hook-form": "^7.55.0", "react-intersection-observer": "^9.16.0", "react-select": "^5.10.1", "recharts": "^2.15.3", "reflect-metadata": "^0.2.2", "sonner": "^1.7.4", "string-similarity": "^4.0.4", "swr": "^2.3.3", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "tldts": "^7.0.12", "typeorm": "^0.3.25", "uuid": "^11.1.0", "xlsx": "^0.18.5", "zlib": "^1.0.5", "zod": "^3.24.3"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@types/bcryptjs": "^3.0.0", "@types/jest": "^29.5.14", "@types/lodash": "^4.17.16", "@types/node": "^20.19.10", "@types/react": "^18.3.20", "@types/react-dom": "^18.3.6", "@types/string-similarity": "^4.0.2", "@types/uuid": "^10.0.0", "@types/xlsx": "^0.0.35", "concurrently": "^8.2.2", "cross-env": "^7.0.3", "dotenv": "^17.0.0", "eslint": "^9.24.0", "eslint-config-next": "15.0.2", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.5", "jest-environment-node": "^29.7.0", "node-fetch": "^3.3.2", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "ts-jest": "^29.4.0", "tsx": "^4.20.3", "typescript": "^5.8.3"}}